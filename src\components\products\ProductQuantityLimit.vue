<template>
    <div class="space-y-6">
        <div class="form-group w-full">
            <div class="switch-container switch-left">
                <IluriaToggleSwitch
                    id="minQuantityLimitSwitch"
                    :modelValue="minEnabled"
                    @update:modelValue="toggleMinEnabled"
                    :label="t('product.minQuantityLimitCheck')"
                    labelPosition="horizontal"
                />
            </div>
            <div v-if="minEnabled" class="mt-4 input-aligned">
                <IluriaInputText
                    id="minQuantityLimit"
                    name="minQuantityLimit"
                    type="number"
                    :suffix="getUnitText(minQuantityLimit)"
                    v-model.number="minQuantityLimit"
                    :formContext="props.formContext?.minQuantityLimit"
                    placeholder="1"
                    min="1"
                    required
                    :error="getMinError()"
                />
            </div>
        </div>
        <div class="form-group w-full">
            <div class="switch-container switch-left">
                <IluriaToggleSwitch
                    id="maxQuantityLimitSwitch"
                    :modelValue="maxEnabled"
                    @update:modelValue="toggleMaxEnabled"
                    :label="t('product.maxQuantityLimitCheck')"
                    labelPosition="horizontal"
                />
            </div>
            <div v-if="maxEnabled" class="mt-4 input-aligned">
                <IluriaInputText
                    id="maxQuantityLimit"
                    name="maxQuantityLimit"
                    type="number"
                    :suffix="getUnitText(maxQuantityLimit)"
                    v-model.number="maxQuantityLimit"
                    :formContext="props.formContext?.maxQuantityLimit"
                    :placeholder="minEnabled && minQuantityLimit ? String(Number(minQuantityLimit) + 1) : '2'"
                    :min="minEnabled && minQuantityLimit ? Number(minQuantityLimit) + 1 : 1"
                    required
                    :error="getMaxError()"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineModel, ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';

const { t } = useI18n();

const minQuantityLimit = defineModel('minQuantityLimit', { default: null });
const maxQuantityLimit = defineModel('maxQuantityLimit', { default: null });

const props = defineProps({
    formContext: {
        type: Object,
        default: () => ({})
    }
});

const minEnabled = ref(minQuantityLimit.value !== null && minQuantityLimit.value !== undefined);
const maxEnabled = ref(maxQuantityLimit.value !== null && maxQuantityLimit.value !== undefined);

// Função para retornar o texto da unidade baseado na quantidade
const getUnitText = (quantity) => {
  const numValue = Number(quantity) || 0;
  return numValue > 1 ? 'unidades' : 'unidade';
};

const toggleMinEnabled = (value) => {
    minEnabled.value = value;
    if (!value) {
        minQuantityLimit.value = null;
    } else if (minQuantityLimit.value === null || minQuantityLimit.value === undefined || minQuantityLimit.value === '') {
        minQuantityLimit.value = 1; // Valor padrão mínimo de 1
    }
};

const toggleMaxEnabled = (value) => {
    maxEnabled.value = value;
    if (!value) {
        maxQuantityLimit.value = null;
    } else if (maxQuantityLimit.value === null || maxQuantityLimit.value === undefined || maxQuantityLimit.value === '') {
        // Se há quantidade mínima definida, define máxima como mínima + 1, senão define como 2
        const minValue = minEnabled.value && minQuantityLimit.value ? Number(minQuantityLimit.value) : 1;
        maxQuantityLimit.value = minValue + 1;
    }
};

watch(() => minQuantityLimit.value, (newValue) => {
    minEnabled.value = newValue !== null && newValue !== undefined;
});

watch(() => maxQuantityLimit.value, (newValue) => {
    maxEnabled.value = newValue !== null && newValue !== undefined;
});

const getMinError = () => {
    if (minEnabled.value && (minQuantityLimit.value === '' || minQuantityLimit.value === null || minQuantityLimit.value === undefined)) {
        return t('validation.required');
    }
    if (minEnabled.value && minQuantityLimit.value !== '' && Number(minQuantityLimit.value) < 1) {
        return t('validation.minQuantityMustBeAtLeastOne');
    }
    if (minEnabled.value && maxEnabled.value &&
        minQuantityLimit.value !== '' && maxQuantityLimit.value !== '' &&
        Number(minQuantityLimit.value) > Number(maxQuantityLimit.value)) {
        return t('validation.minGreaterThanMax');
    }
    return '';
};

const getMaxError = () => {
    if (maxEnabled.value && (maxQuantityLimit.value === '' || maxQuantityLimit.value === null || maxQuantityLimit.value === undefined)) {
        return t('validation.required');
    }
    if (maxEnabled.value && minEnabled.value &&
        maxQuantityLimit.value !== '' && minQuantityLimit.value !== '' &&
        Number(maxQuantityLimit.value) <= Number(minQuantityLimit.value)) {
        return t('validation.maxMustBeGreaterThanMin');
    }
    return '';
};

const isValid = computed(() => {
    // Validação de campos obrigatórios
    const requiredFieldsValid = !(minEnabled.value && (minQuantityLimit.value === '' || minQuantityLimit.value === null || minQuantityLimit.value === undefined))
        && !(maxEnabled.value && (maxQuantityLimit.value === '' || maxQuantityLimit.value === null || maxQuantityLimit.value === undefined));

    // Validação de quantidade mínima >= 1
    const minQuantityValid = !(minEnabled.value && minQuantityLimit.value !== '' && Number(minQuantityLimit.value) < 1);

    // Validação de quantidade máxima > quantidade mínima
    const minMaxValid = !(minEnabled.value && maxEnabled.value &&
        minQuantityLimit.value !== '' && maxQuantityLimit.value !== '' &&
        Number(maxQuantityLimit.value) <= Number(minQuantityLimit.value));

    return requiredFieldsValid && minQuantityValid && minMaxValid;
});

defineExpose({
    isValid
});
</script>

<style scoped>
.switch-container {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.switch-left :deep(.flex.items-center.gap-2) {
    flex-direction: row-reverse;
    justify-content: flex-start;
    align-items: center;
    width: auto;
    margin: 0;
    padding: 0;
}

.switch-left :deep(.custom-toggle-switch) {
    margin-right: 8px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group:last-child {
    margin-bottom: 0;
}
</style>
