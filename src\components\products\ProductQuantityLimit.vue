<template>
    <div class="space-y-6">
        <div class="form-group w-full">
            <div class="switch-container">
                <div class="switch-content">
                    <div class="switch-info">
                        <h4 class="switch-title">{{ t('product.minQuantityLimitCheck') }}</h4>
                    </div>
                    <IluriaToggleSwitch
                        id="minQuantityLimitSwitch"
                        :modelValue="minEnabled"
                        @update:modelValue="toggleMinEnabled"
                        labelPosition="horizontal"
                    />
                </div>
            </div>
            <div v-if="minEnabled" class="mt-4">
                <IluriaInputText
                    id="minQuantityLimit"
                    name="minQuantityLimit"
                    type="number"
                    :suffix="getUnitText(minQuantityLimit)"
                    :label="t('product.minQuantityLimit')"
                    v-model.number="minQuantityLimit"
                    :formContext="props.formContext?.minQuantityLimit"
                    placeholder="0"
                    required
                    :error="getMinError()"
                />
            </div>
        </div>
        <div class="form-group w-full">
            <div class="switch-container">
                <div class="switch-content">
                    <div class="switch-info">
                        <h4 class="switch-title">{{ t('product.maxQuantityLimitCheck') }}</h4>
                    </div>
                    <IluriaToggleSwitch
                        id="maxQuantityLimitSwitch"
                        :modelValue="maxEnabled"
                        @update:modelValue="toggleMaxEnabled"
                        labelPosition="horizontal"
                    />
                </div>
            </div>
            <div v-if="maxEnabled" class="mt-4">
                <IluriaInputText
                    id="maxQuantityLimit"
                    name="maxQuantityLimit"
                    type="number"
                    :suffix="getUnitText(maxQuantityLimit)"
                    :label="t('product.maxQuantityLimit')"
                    v-model.number="maxQuantityLimit"
                    :formContext="props.formContext?.maxQuantityLimit"
                    placeholder="0"
                    required
                    :error="getMaxError()"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineModel, ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';

const { t } = useI18n();

const minQuantityLimit = defineModel('minQuantityLimit', { default: null });
const maxQuantityLimit = defineModel('maxQuantityLimit', { default: null });

const props = defineProps({
    formContext: {
        type: Object,
        default: () => ({})
    }
});

const minEnabled = ref(minQuantityLimit.value !== null && minQuantityLimit.value !== undefined);
const maxEnabled = ref(maxQuantityLimit.value !== null && maxQuantityLimit.value !== undefined);

// Função para retornar o texto da unidade baseado na quantidade
const getUnitText = (quantity) => {
  const numValue = Number(quantity) || 0;
  return numValue > 1 ? 'unidades' : 'unidade';
};

const toggleMinEnabled = (value) => {
    minEnabled.value = value;
    if (!value) {
        minQuantityLimit.value = null;
    } else if (minQuantityLimit.value === null || minQuantityLimit.value === undefined) {
        minQuantityLimit.value = '';
    }
};

const toggleMaxEnabled = (value) => {
    maxEnabled.value = value;
    if (!value) {
        maxQuantityLimit.value = null;
    } else if (maxQuantityLimit.value === null || maxQuantityLimit.value === undefined) {
        maxQuantityLimit.value = '';
    }
};

watch(() => minQuantityLimit.value, (newValue) => {
    minEnabled.value = newValue !== null && newValue !== undefined;
});

watch(() => maxQuantityLimit.value, (newValue) => {
    maxEnabled.value = newValue !== null && newValue !== undefined;
});

const getMinError = () => {
    if (minEnabled.value && minQuantityLimit.value === '') {
        return t('validation.required');
    }
    if (minEnabled.value && maxEnabled.value && 
        minQuantityLimit.value !== '' && maxQuantityLimit.value !== '' && 
        Number(minQuantityLimit.value) > Number(maxQuantityLimit.value)) {
        return t('validation.minGreaterThanMax');
    }
    return '';
};

const getMaxError = () => {
    if (maxEnabled.value && maxQuantityLimit.value === '') {
        return t('validation.required');
    }
    return '';
};

const isValid = computed(() => {
    const requiredFieldsValid = !(minEnabled.value && minQuantityLimit.value === '') 
        && !(maxEnabled.value && maxQuantityLimit.value === '');
    
    const minMaxValid = !(minEnabled.value && maxEnabled.value && 
        minQuantityLimit.value !== '' && maxQuantityLimit.value !== '' && 
        Number(minQuantityLimit.value) > Number(maxQuantityLimit.value));
    
    return requiredFieldsValid && minMaxValid;
});

defineExpose({
    isValid
});
</script>

<style scoped>
.switch-container {
    padding: 16px 0;
    border-bottom: 1px solid var(--iluria-color-border-light, #e5e7eb);
}

.switch-container:last-child {
    border-bottom: none;
}

.switch-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.switch-info {
    flex: 1;
}

.switch-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--iluria-color-text-primary, #374151);
    margin: 0;
    line-height: 1.4;
}

.form-group {
    margin-bottom: 0;
}

.space-y-6 > * + * {
    margin-top: 0;
}
</style>
