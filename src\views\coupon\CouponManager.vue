<template>
  <div class="coupon-manager-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="t('couponManager.title')"
      :subtitle="t('couponManager.subtitle')"
      :showSearch="hasCoupons"
      :showAdd="true"
      :addText="t('couponManager.newCoupon')"
      :searchPlaceholder="t('couponManager.searchPlaceholder')"
      @search="handleSearch"
      @add-click="goToAddCoupon"
    />

    <!-- Table Wrapper -->
    <div class="table-wrapper">
      <IluriaDataTable
        :value="sortedCoupons"
        :columns="hasCoupons ? mainTableColumns : []"
        :loading="loading"
        dataKey="id"
        class="coupons-table"
      >
        <!-- Header Slots -->
        <template #header-checkbox>
          <span class="column-header checkbox-header">
          </span>
        </template>
        <template #header-code>
          <span class="column-header" data-sortable="true" @click="toggleSort('code')">
            {{ t('couponManager.code') }}
            <span v-if="sortField === 'code'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-description>
          <span class="column-header" data-sortable="true" @click="toggleSort('description')">
            {{ t('couponManager.description') }}
            <span v-if="sortField === 'description'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-discountType>
          <span class="column-header" data-sortable="true" @click="toggleSort('discountType')">
            {{ t('couponManager.type') }}
            <span v-if="sortField === 'discountType'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-discountValue>
          <span class="column-header" data-sortable="true" @click="toggleSort('discountValue')">
            {{ t('couponManager.value') }}
            <span v-if="sortField === 'discountValue'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-status>
          <span class="column-header" data-sortable="true" @click="toggleSort('status')">
            {{ t('couponManager.status') }}
            <span v-if="sortField === 'status'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-validity>
          <span class="column-header" data-sortable="true" @click="toggleSort('validity')">
            {{ t('couponManager.validity') }}
            <span v-if="sortField === 'validity'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-actions>
          <span class="column-header">{{ t('couponManager.actions') }}</span>
        </template>

        <!-- Column Templates -->
        <template #column-checkbox="{ data }">
          <div class="checkbox-cell">
            <IluriaCheckbox
              :model-value="isCouponSelected(data.id)"
              :label="''"
              :disabled="loading"
              :aria-describedBy="t('couponManager.selectCoupon') + ' ' + data.code"
              class="coupon-selection-checkbox"
              @update:model-value="toggleCouponSelection(data.id)"
            />
          </div>
        </template>
        <template #column-code="{ data }">
          <span class="coupon-code">{{ data.code }}</span>
        </template>

        <!-- Description Column -->
        <template #column-description="{ data }">
          <span class="coupon-description">{{ data.description }}</span>
        </template>

        <!-- Discount Type Column -->
        <template #column-discountType="{ data }">
          <span class="discount-type-badge"
            :class="{
              'badge-percentage': data.discountType.includes('PERCENTAGE'),
              'badge-fixed': data.discountType === 'FIXED_TOTAL',
              'badge-shipping': data.discountType === 'FREE_SHIPPING',
              'badge-gift': data.discountType === 'GIFT_COUPON'
            }">
            {{
              data.discountType.includes('PERCENTAGE') ? t('couponManager.percentage') :
              data.discountType === 'FIXED_TOTAL' ? t('couponManager.fixedValue') :
              data.discountType === 'FREE_SHIPPING' ? t('couponManager.freeShipping') :
              t('couponManager.gift')
            }}
          </span>
        </template>

        <!-- Discount Value Column -->
        <template #column-discountValue="{ data }">
          <span class="discount-value">{{ formatDiscountValue(data) }}</span>
        </template>

        <!-- Status Column -->
        <template #column-status="{ data }">
          <span class="status-badge" 
            :class="data.active ? 'status-active' : 'status-inactive'">
            {{ data.active ? t('couponManager.active') : t('couponManager.inactive') }}
          </span>
        </template>

        <!-- Validity Column -->
        <template #column-validity="{ data }">
          <div class="validity-dates">
            <div class="date-range">{{ formatDate(data.startsAt) }} - {{ formatDate(data.endsAt) }}</div>
          </div>
        </template>

        <!-- Actions Column -->
        <template #column-actions="{ data }">
          <div class="action-buttons">
            <IluriaButton 
              color="text-primary" 
              size="small" 
              :hugeIcon="PencilEdit01Icon" 
              @click="editCoupon(data.id)"
              :title="t('couponManager.editCoupon')"
            />
            <IluriaButton 
              color="text-danger" 
              size="small" 
              :hugeIcon="Delete01Icon" 
              @click="confirmDeleteCoupon(data)"
              :title="t('couponManager.deleteCoupon')"
            />
            <IluriaButton 
              color="text-purple-600" 
              size="small" 
              :hugeIcon="TransactionHistoryIcon" 
              @click="viewUsageHistory(data)"
              :title="t('couponManager.viewHistory')"
            />
          </div>
        </template>

        <!-- Empty State -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <CouponPercentIcon class="w-12 h-12" />
            </div>
            <h3 class="empty-title">{{ t('couponManager.noCoupons') }}</h3>
            <p class="empty-description">{{ t('couponManager.noCouponsDescription') }}</p>
            <IluriaButton 
              color="dark"
              :hugeIcon="Add01Icon"
              @click="goToAddCoupon"
              class="mt-4"
            >
              {{ t('couponManager.createFirstCoupon') }}
            </IluriaButton>
          </div>
        </template>

        <!-- Loading State -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>{{ t('couponManager.loadingCoupons') }}</span>
          </div>
        </template>
      </IluriaDataTable>
    </div>

    <!-- Bulk Action Bar -->
    <IluriaBulkActionBar
      :selected-count="selectedCoupons.size"
      :actions="bulkActions"
      :loading="false"
      :disabled="loading"
      entity-name="cupom"
      entity-name-plural="cupons"
      @action-click="handleBulkActionClick"
    />

    <!-- Pagination -->
    <div class="pagination-container" v-if="totalPages > 0">
      <IluriaPagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @go-to-page="changePage"
      />
    </div>
    
    <!-- Modal de Histórico de Uso -->
    <IluriaModal 
      :model-value="showUsageHistoryModal" 
      :title="t('couponManager.usageHistoryTitle')" 
      :dialog-style="{ width: '60vw' }" 
      @update:model-value="showUsageHistoryModal = $event"
      class="usage-modal"
    >
      <div v-if="loadingUsage" class="loading-state">
        <div class="loading-spinner"></div>
        <p>{{ t('couponManager.loadingHistory') }}</p>
      </div>
      <div v-else class="usage-content">
        <div class="usage-header">
          <h3 class="usage-title">{{ t('couponManager.coupon') }}: {{ selectedCouponForUsage?.code }}</h3>
          <p class="usage-description">{{ selectedCouponForUsage?.description }}</p>
        </div>
        <IluriaDataTable 
          :value="couponUsageHistory" 
          :columns="couponUsageHistory.length > 0 ? usageHistoryColumns : []"
          class="usage-history-table"
        >
          <template #column-customerName="{ data }">
            <span>{{ data.customerName }}</span>
          </template>
          
          <template #column-customerEmail="{ data }">
            <span>{{ data.customerEmail }}</span>
          </template>
          
          <template #column-uses="{ data }">
            <span>{{ data.uses }}</span>
          </template>
          
          <template #column-usageLimit="{ data }">
            <span>{{ data.usageLimit }}</span>
          </template>
          
          <template #column-status="{ data }">
            <span class="status-badge" :class="data.status === 'active' ? 'status-available' : 'status-unavailable'">
              {{ data.status === 'active' ? t('couponManager.available') : t('couponManager.unavailable') }}
            </span>
          </template>

          <template #empty>
            <div class="empty-history">
              <TransactionHistoryIcon class="w-8 h-8 text-gray-400" />
              <p>{{ t('couponManager.noUsageHistory') }}</p>
            </div>
          </template>
        </IluriaDataTable>
      </div>
      <template #footer>
        <IluriaButton @click="showUsageHistoryModal = false" color="dark" variant="outline">
          {{ t('couponManager.close') }}
        </IluriaButton>
      </template>
    </IluriaModal>

    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { PencilEdit01Icon, Delete01Icon, CouponPercentIcon, Add01Icon, TransactionHistoryIcon } from '@hugeicons-pro/core-stroke-rounded'
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import CouponService from '@/services/coupon.service'
import CustomerService from '@/services/customer.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaBulkActionBar from '@/components/iluria/IluriaBulkActionBar.vue'
import IluriaCheckbox from '@/components/iluria/form/IluriaCheckbox.vue'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const toast = useToast()
const { t } = useI18n()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const coupons = ref([])
const loading = ref(true)
const currentPage = ref(0)
const totalPages = ref(0)
const filters = ref({ filter: '' })
const showUsageHistoryModal = ref(false)
const selectedCouponForUsage = ref(null)
const couponUsageHistory = ref([])
const loadingUsage = ref(false)
const hasCoupons = ref(false)

// Bulk selection state
const selectedCoupons = ref(new Set())

const sortField = ref(null)
const sortOrder = ref(null)

const toggleSort = (field) => {
  if (sortField.value !== field) {
    sortField.value = field
    sortOrder.value = 1
  } else if (sortOrder.value === 1) {
    sortOrder.value = -1
  } else {
    sortField.value = null
    sortOrder.value = null
  }
}

const sortedCoupons = computed(() => {
  if (!sortField.value || !sortOrder.value) return coupons.value
  
  const sorted = [...coupons.value]
  
  sorted.sort((a, b) => {
    let valA = a[sortField.value]
    let valB = b[sortField.value]

    if (sortField.value === 'status') {
      valA = a.active
      valB = b.active
      return sortOrder.value * (valA === valB ? 0 : valA ? -1 : 1)
    }
    
    if (sortField.value === 'validity') {
      valA = a.startsAt ? new Date(a.startsAt).getTime() : 0
      valB = b.startsAt ? new Date(b.startsAt).getTime() : 0
    }

    if (typeof valA === 'string') {
      return sortOrder.value * valA.localeCompare(valB)
    }
    
    if (typeof valA === 'number') {
      return sortOrder.value * (valA - valB)
    }

    return 0
  })
  
  return sorted
})

// Configuração das colunas da tabela principal
const mainTableColumns = [
  { field: 'checkbox', headerClass: 'col-checkbox', class: 'col-checkbox' },
  { field: 'code', headerClass: 'col-small', class: 'col-small' },
  { field: 'description', headerClass: 'col-large', class: 'col-large' },
  { field: 'discountType', headerClass: 'col-medium', class: 'col-medium' },
  { field: 'discountValue', headerClass: 'col-medium', class: 'col-medium' },
  { field: 'status', headerClass: 'col-small', class: 'col-small' },
  { field: 'validity', headerClass: 'col-date', class: 'col-date' },
  { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
]

// Configuração das colunas da tabela de histórico de uso
const usageHistoryColumns = [
  { field: 'customerName', header: t('name'), maxLength: 30 },
  { field: 'customerEmail', header: t('login.email'), maxLength: 35 },
  { field: 'uses', header: t('couponManager.uses'), maxLength: 10 },
  { field: 'usageLimit', header: t('couponManager.limit'), maxLength: 10 },
  { field: 'status', header: t('status'), maxLength: 15 }
]

// Carregar cupons com paginação
const loadCoupons = async () => {
  loading.value = true
  try {
    const response = await CouponService.listCoupons(filters.value.filter, currentPage.value, 5)
    coupons.value = response.content  
    totalPages.value = response.totalPages
    hasCoupons.value = response.content.length > 0
  } catch (error) {
    console.error('Erro ao carregar cupons:', error)
    toast.add({ severity: 'error', summary: t('error'), detail: t('couponManager.couponsLoadError'), life: 3000 })
  } finally {
    loading.value = false
  }
}

const formatDiscountValue = (coupon, locale = 'pt-BR') => {
    if (!coupon) return ''
    
    const currencyFormat = new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: locale === 'pt-BR' ? 'BRL' : 'USD',
    })

    switch(coupon.discountType) {
        case 'PERCENTAGE_PRODUCTS':
        case 'PERCENTAGE_TOTAL':
        case 'PERCENTAGE_SHIPPING':
            return coupon.discountValue 
                ? `${coupon.discountValue}%` 
                : '0%'
            
        case 'FIXED_TOTAL':
            return coupon.discountValue 
                ? currencyFormat.format(coupon.discountValue)
                : currencyFormat.format(0)
        default:
            return ''
    }
}

const changePage = (page) => {
  currentPage.value = page
  loadCoupons()
}

// Formatar data
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('pt-BR')
}

// Navegar para página de adição de cupom
const goToAddCoupon = () => {
  router.push('/coupons/new')
}

// Editar cupom
const editCoupon = (couponId) => {
  router.push(`/coupons/${couponId}`)
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('delete')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

// Confirmar exclusão de cupom
const confirmDeleteCoupon = (coupon) => {
  showConfirmDanger(
    `${t('couponManager.confirmDeleteMessage')} ${coupon.code}?`,
    t('couponManager.confirmDeleteTitle'),
    () => deleteCoupon(coupon.id)
  )
}

// Excluir cupom
const deleteCoupon = async (couponId) => {
  try {
    loading.value = true
    await CouponService.deleteCoupon(couponId)
    toast.add({ severity: 'success', summary: t('success'), detail: t('couponManager.couponDeleted'), life: 3000 })
    loadCoupons()
  } catch (error) {
    console.error('Erro ao excluir cupom:', error)
    toast.add({ severity: 'error', summary: t('error'), detail: t('couponManager.couponDeleteError'), life: 3000 })
  } finally {
    loading.value = false
  }
}

// Visualizar histórico de uso
const viewUsageHistory = (coupon) => {
  selectedCouponForUsage.value = coupon
  showUsageHistoryModal.value = true
  loadCouponUsageHistory()
}

// Carregar histórico de uso do cupom
const loadCouponUsageHistory = async () => {
  try {
    loadingUsage.value = true
    const response = await CouponService.getCoupon(selectedCouponForUsage.value.id)
    
    if (response && response.couponUsages) {
      couponUsageHistory.value = response.couponUsages
      
      for (const usage of couponUsageHistory.value) {
        try {
          const customerInfo = await CustomerService.getCustomer(usage.customerId)
          if (customerInfo) {
            usage.customerName = customerInfo.name || t('validation.name-unavailable')
            usage.customerEmail = customerInfo.email || t('validation.email-unavailable')
          } else {
            usage.customerName = t('validation.customer-not-found')
            usage.customerEmail = t('validation.email-unavailable')
          }
        } catch (error) {
          usage.customerName = t('validation.error-fetching-customer')
          usage.customerEmail = t('validation.error-fetching-email')
        }
      }
    } else {
      couponUsageHistory.value = []
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: t('error'), detail: t('couponManager.usageHistoryLoadError'), life: 3000 })
    couponUsageHistory.value = []
  } finally {
    loadingUsage.value = false
  }
}

// Debounce para busca
let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 0
    loadCoupons()
  }, 400)
}

const handleSearch = (searchValue) => {
  filters.value.filter = searchValue
  debouncedSearch()
}

// Bulk selection functions
const toggleCouponSelection = (couponId) => {
  const newSelection = new Set(selectedCoupons.value)
  if (newSelection.has(couponId)) {
    newSelection.delete(couponId)
  } else {
    newSelection.add(couponId)
  }
  selectedCoupons.value = newSelection
}

const clearSelection = () => {
  selectedCoupons.value = new Set()
}

const isCouponSelected = (couponId) => {
  return selectedCoupons.value.has(couponId)
}

// Bulk delete
const confirmBulkDelete = () => {
  const count = selectedCoupons.value.size
  const entityKey = count === 1 ? 'couponManager.coupon' : 'couponManager.coupons'
  const messageKey = count === 1 ? 'couponManager.confirmBulkDeleteMessageSingle' : 'couponManager.confirmBulkDeleteMessage'

  showConfirmDanger(
    t(messageKey, { count, entity: t(entityKey) }),
    t('couponManager.confirmBulkDeleteTitle'),
    () => bulkDeleteCoupons()
  )
}

const bulkDeleteCoupons = async () => {
  if (selectedCoupons.value.size === 0) return

  try {
    const couponIds = Array.from(selectedCoupons.value)
    const response = await CouponService.deleteCoupons(couponIds)

    const count = response.deletedCount || selectedCoupons.value.size
    const entityKey = count === 1 ? 'couponManager.coupon' : 'couponManager.coupons'
    const messageKey = count === 1 ? 'couponManager.bulkDeleteSuccessSingle' : 'couponManager.bulkDeleteSuccess'

    toast.add({
      severity: 'success',
      summary: t('success'),
      detail: t(messageKey, { count, entity: t(entityKey) }),
      life: 3000
    })

    clearSelection()
    loadCoupons()
  } catch (error) {
    console.error('Error bulk deleting coupons:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: t('couponManager.bulkDeleteError', { entity: t('couponManager.coupons') }),
      life: 3000
    })
  }
}

// Bulk actions for the action bar
const bulkActions = computed(() => [
  {
    text: t('couponManager.deleteSelected'),
    icon: Delete01Icon,
    color: 'danger',
    variant: 'solid',
    callback: confirmBulkDelete,
    loading: false
  }
])

const handleBulkActionClick = () => {
  // O evento é tratado pelo IluriaBulkActionBar que chama action.callback automaticamente
  // Este handler pode ser usado para lógica adicional se necessário
}

// Carregar dados ao montar o componente
onMounted(() => {
  loadCoupons()
})
</script>

<style scoped>
.coupon-manager-container {
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 0.3s ease;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  min-width: 250px;
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
  overflow-x: auto;
  width: 100%;
}

/* Column width definitions */
:deep(.col-checkbox) { width: 40px; min-width: 40px; max-width: 40px; text-align: center; }
:deep(.col-small) { width: 100px; min-width: 100px; text-align: center; }
:deep(.col-medium) { width: 180px; min-width: 180px; text-align: center; }
:deep(.col-large) { width: 220px; min-width: 220px; text-align: center; }
:deep(.col-date) { width: 280px; min-width: 280px; max-width: 280px; text-align: center; }
:deep(.col-actions) { width: 120px; min-width: 120px; text-align: center; }

/* Column header style */
:deep(.coupons-table th .column-header) {
  display: block;
  width: 100%;
  text-align: center;
  cursor: default;
  user-select: none;
}

:deep(.coupons-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

:deep(.coupons-table .p-datatable-table) {
  table-layout: fixed;
  width: 100%;
}

:deep(.coupons-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  border-bottom: 1px solid var(--iluria-color-border);
}

/* Específico para coluna de data */
:deep(.coupons-table .p-datatable-tbody > tr > td.col-date) {
  padding: 16px 8px;
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  width: 280px !important;
  min-width: 280px !important;
  max-width: 280px !important;
}

:deep(.coupons-table .p-datatable-thead > tr > th.col-date) {
  width: 280px !important;
  min-width: 280px !important;
  max-width: 280px !important;
}

.coupon-code, .coupon-description, .discount-value {
  display: block;
  text-align: center;
}

.validity-dates {
  text-align: center;
  width: 100%;
  display: block;
  white-space: nowrap;
  overflow: visible;
}



.discount-type-badge, .status-badge {
  margin: 0 auto;
}

:deep(.coupons-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  transition: all 0.3s ease;
}

:deep(.coupons-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px 24px;
  transition: all 0.3s ease;
}

:deep(.coupons-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
  transition: background-color 0.2s ease;
}

:deep(.coupons-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}



:deep(.usage-history-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  transition: all 0.3s ease;
}

:deep(.usage-history-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px 24px;
  transition: all 0.3s ease;
}

:deep(.usage-history-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
  transition: background-color 0.2s ease;
}

:deep(.usage-history-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.usage-history-table .p-datatable-tbody > tr > td) {
  padding: 16px 24px;
  border: none;
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  transition: all 0.3s ease;
}

/* Coupon Elements */
.coupon-code {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  font-family: monospace;
  transition: color 0.3s ease;
}

.coupon-description {
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}

/* Badges */
.discount-type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  display: inline-block;
}

.badge-percentage {
  background: #dbeafe;
  color: #1d4ed8;
}

.badge-fixed {
  background: #dcfce7;
  color: #166534;
}

.badge-shipping {
  background: #f3e8ff;
  color: #7c3aed;
}

.badge-gift {
  background: #f3f4f6;
  color: #374151;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: #dcfce7;
  color: #166534;
}

.status-inactive {
  background: #fee2e2;
  color: #dc2626;
}

.status-available {
  background: #dcfce7;
  color: #166534;
}

.status-unavailable {
  background: #fee2e2;
  color: #dc2626;
}

/* Values */
.discount-value {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}



.date-range {
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s ease;
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  display: block;
  width: 100%;
}

/* Actions */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  background: var(--iluria-color-surface);
  transition: background-color 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  color: var(--iluria-color-text-muted);
  transition: color 0.3s ease;
}



.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  transition: color 0.3s ease;
  text-align: center;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 16px 0;
  transition: color 0.3s ease;
  text-align: center;
  line-height: 1.5;
  max-width: 400px;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
  background: var(--iluria-color-surface);
  transition: all 0.3s ease;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Modal */
.usage-content {
  padding: 16px 0;
}

.usage-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.usage-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  transition: color 0.3s ease;
}

.usage-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  transition: color 0.3s ease;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 24px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s ease;
}

.empty-history p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1024px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .search-input {
    min-width: 200px;
  }
}

/* Checkbox styling */
.checkbox-cell {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
  margin: 0 !important;
}

.checkbox-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
  margin: 0 !important;
}

.coupon-selection-checkbox {
  margin: 0 !important;
  padding: 0 !important;
  width: 16px !important;
  height: 16px !important;
}

.coupon-selection-checkbox :deep(.iluria-checkbox-wrapper) {
  width: 16px !important;
  margin: 0 !important;
  padding: 0 !important;
}

.coupon-selection-checkbox :deep(.checkbox-label) {
  display: none !important;
}

.coupon-selection-checkbox :deep(.iluria-checkbox) {
  margin: 0 !important;
  padding: 0 !important;
  flex-shrink: 0;
  width: 16px !important;
  height: 16px !important;
}

/* Compact table cell overrides */
:deep(.coupons-table .p-datatable-tbody > tr > td.col-checkbox) {
  padding: 4px 2px !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}

:deep(.coupons-table .p-datatable-thead > tr > th.col-checkbox) {
  padding: 8px 2px !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}

@media (max-width: 768px) {
  .coupon-manager-container {
    padding: 16px;
  }

  .search-input {
    min-width: 150px;
  }

  :deep(.coupons-table .p-datatable-tbody > tr > td) {
    padding: 12px 16px;
  }

  :deep(.coupons-table .p-datatable-thead > tr > th) {
    padding: 12px 16px;
    font-size: 11px;
  }

  /* Ajuste para coluna de data em telas pequenas */
  :deep(.col-date) {
    width: 240px !important;
    min-width: 240px !important;
    max-width: 240px !important;
  }

  :deep(.coupons-table .p-datatable-tbody > tr > td.col-date) {
    width: 240px !important;
    min-width: 240px !important;
    max-width: 240px !important;
  }

  :deep(.coupons-table .p-datatable-thead > tr > th.col-date) {
    width: 240px !important;
    min-width: 240px !important;
    max-width: 240px !important;
  }

  .validity-dates {
    font-size: 12px;
  }

  .date-range {
    white-space: normal;
    line-height: 1.3;
  }
}
</style>
