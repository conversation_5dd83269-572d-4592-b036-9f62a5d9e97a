<template>
    <div class="store-physical-data-container">
      <!-- Header Section -->
      <IluriaHeader
        :title="$t('storePhysicalData.title')"
        :subtitle="$t('storePhysicalData.subtitle')"
        :showSave="true"
        :saveText="$t('storePhysicalData.saveButton')"
        @save-click="saveData"
      />
      <!-- Main Content -->
      <Form 
        v-if="dataLoaded" 
        @submit="saveData" 
        :initial-values="formData" 
        class="form-container"
      >
        
        <!-- <PERSON><PERSON> da Loja -->
        <ViewContainer 
          :title="$t('storePhysicalData.storeDataTitle')"
          :subtitle="$t('storePhysicalData.storeDataSubtitle')"
          :icon="Store01Icon"
          iconColor="blue"
        >
          <div class="form-grid">
            <!-- Nome da Loja -->
            <div class="form-field">
              <IluriaInputText 
                id="storeName"
                :label="$t('storePhysicalData.storeName')"
                v-model="formData.storeName"
              />
            </div>
            <!-- Tipo da Loja -->
            <div class="form-field">
              <IluriaLabel :for="'storeType'" labelClass="mb-2 block">
                {{ $t('storePhysicalData.storeType') }}
              </IluriaLabel>
              <IluriaSelect
                id="storeType"
                v-model="formData.storeType"
                :options="storeTypeOptions"
                optionLabel="label"
                optionValue="value"
              />
            </div>
            <!-- Documento (CPF/CNPJ) -->
            <div class="form-field">
              <IluriaInputText 
                id="document"
                :label="getDocumentLabel()"
                v-model="formData.document" 
                :mask="getDocumentMask()"
              />
            </div>
            <!-- CEP -->
            <div class="form-field">
              <div class="zip-code-field">
                <IluriaInputText 
                  id="zipCode"
                  :label="$t('storePhysicalData.zipCode')"
                  v-model="formData.zipCode" 
                  mask="cep"
                  :disabled="isLoadingCEP"
                />
                <div v-if="isLoadingCEP" class="loading-indicator">
                  <div class="spinner"></div>
                  <span class="loading-text">Buscando CEP...</span>
                </div>
              </div>
            </div>
            <!-- Endereço -->
            <div class="form-field">
              <IluriaInputText 
                id="address"
                :label="$t('storePhysicalData.address')"
                v-model="formData.address"
              />
            </div>
            <!-- Complemento -->
            <div class="form-field">
              <IluriaInputText 
                id="complement"
                :label="$t('storePhysicalData.complement')"
                v-model="formData.complement"
              />
            </div>
            <!-- Cidade -->
            <div class="form-field">
              <IluriaInputText 
                id="city"
                :label="$t('storePhysicalData.city')"
                v-model="formData.city"
              />
            </div>
            <!-- Estado -->
            <div class="form-field">
              <IluriaInputText 
                id="state"
                :label="$t('storePhysicalData.state')"
                v-model="formData.state"
              />
            </div>
            <!-- Telefone -->
            <div class="form-field">
              <IluriaInputText 
              id="phone"
              :label="$t('storePhysicalData.phone')"
              v-model="formData.phone" 
              mask="phone"
              />
            </div>
            <!-- Email -->
            <div class="form-field">
              <IluriaInputText 
                id="email"
                :label="$t('storePhysicalData.email')"
                v-model="formData.email" 
                type="email"
              />
            </div>
          </div>
        </ViewContainer>

      </Form>
      <!-- Loading State -->
      <div v-else class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">{{ $t('common.loading') }}</p>
      </div>
    </div>
  </template>
  <script setup>
  import { ref, reactive, onMounted, computed, watch } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { Form } from '@primevue/forms';
  import ViewContainer from '@/components/layout/ViewContainer.vue';
  import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
  import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';
  import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
  import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
  import { useToast } from '@/services/toast.service';
  import storePhysicalDataService from '@/services/storePhysicalData.service';
  import CEPAddressService from '@/services/cepAddress.service.js';
  import {
    Store01Icon
  } from '@hugeicons-pro/core-stroke-rounded';
  
  const { t } = useI18n();
  const toast = useToast();
  const dataLoaded = ref(false);
  const isSaving = ref(false);
  const isLoadingCEP = ref(false);
  const formData = reactive({
    storeName: '',
    storeType: 'LEGAL_PERSON',
    document: '',
    address: '',
    complement: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
    email: '',
    phone: ''
  });
  // Opções para tipo de loja
  const storeTypeOptions = computed(() => [
    { label: t('storePhysicalData.personType.fisica'), value: 'LEGAL_PERSON' },
    { label: t('storePhysicalData.personType.juridica'), value: 'LEGAL_ENTITY' }
  ]);
  // Funções para documentos dinâmicos
  const getDocumentLabel = () => {
    return formData.storeType === 'LEGAL_ENTITY' 
      ? t('storePhysicalData.cnpj') 
      : t('storePhysicalData.cpf');
  };
  const getDocumentMask = () => {
    return formData.storeType === 'LEGAL_ENTITY' ? 'cnpj' : 'cpf';
  };

  // Watcher para CEP
  watch(() => formData.zipCode, async (newZipCode, oldZipCode) => {
    if (newZipCode && newZipCode !== oldZipCode) {
      const cleanZipCode = newZipCode.replace(/\D/g, '');
      
      if (cleanZipCode.length === 8) {
        await fullfillAddress(cleanZipCode);
      }
    }
  }, { immediate: false });

  // Função para buscar e preencher endereço via CEP
  const fullfillAddress = async (cep) => {
    if (!cep || cep.length !== 8) {
      return;
    }

    try {
      isLoadingCEP.value = true;
      
      const response = await getCEPAddress(cep);
      
      if (response?.city) {
        // Verifica se TODOS os 4 campos estão vazios antes de preencher
        const allFieldsEmpty = !formData.address && !formData.city && !formData.state && !formData.complement;
        
        if (allFieldsEmpty) {
          formData.address = response.publicArea || '';
          formData.city = response.city || '';
          formData.state = response.state || '';
          formData.complement = response.complement || '';
        }
      }
    } catch (error) {
      console.error('Error fetching CEP address:', error);
      toast.showError('Erro ao conectar com o serviço de CEP. Tente novamente.');
    } finally {
      isLoadingCEP.value = false;
    }
  };

  // Função para chamar o serviço de CEP
  const getCEPAddress = async (cep) => {
    const response = await CEPAddressService.getCEPAddress(cep);
    return response;
  };

  // Carregar dados existentes
  const loadData = async () => {
    try {
      const data = await storePhysicalDataService.getStorePhysicalData();
      
      if (data) {
        // Preencher formulário com dados existentes
        Object.assign(formData, {
          storeName: data.storeName || '',
          storeType: data.storeType || 'LEGAL_PERSON',
          document: data.document || '',
          address: data.address || '',
          complement: data.complement || '',
          city: data.city || '',
          state: data.state || '',
          zipCode: data.zipCode || '',
          country: data.country || '',
          email: data.email || '',
          phone: data.phone || ''
        });
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast.showError(t('storePhysicalData.loadError'));
    } finally {
      dataLoaded.value = true;
    }
  };
  // Salvar dados
  const saveData = async () => {
    if (isSaving.value) return;
    
    // Validação básica dos campos obrigatórios
    const requiredFields = {
      storeName: 'Nome da Loja',
      document: 'Documento',
      email: 'Email',
      phone: 'Telefone',
      address: 'Endereço',
      city: 'Cidade',
      state: 'Estado',
      zipCode: 'CEP'
    };

    for (const [field] of Object.entries(requiredFields)) {
      if (!formData[field] || formData[field].trim() === '') {
        toast.showError(t('storePhysicalData.requiredField'));
        return;
      }
    }
    
    isSaving.value = true;
    
    try {
      // Preparar dados para envio
      const dataToSave = {
        ...formData
      };
      
      await storePhysicalDataService.saveStorePhysicalData(dataToSave);
      
      toast.showSuccess(t('storePhysicalData.saveSuccess'));
    } catch (error) {
      console.error('Erro ao salvar dados:', error);
      toast.showError(t('storePhysicalData.saveError'));
    } finally {
      isSaving.value = false;
    }
  };
  // Carregar dados ao montar componente
  onMounted(() => {
    loadData();
  });
  </script>
  <style scoped>
  .store-physical-data-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
  }
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    gap: 24px;
  }
  .header-content {
    flex: 1;
  }
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--iluria-color-text-primary);
    margin: 0 0 8px 0;
    line-height: 1.3;
  }
  .page-subtitle {
    font-size: 16px;
    color: var(--iluria-color-text-secondary);
    margin: 0;
    line-height: 1.5;
  }
  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  .form-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    align-items: start;
  }
  .form-field {
    display: flex;
    flex-direction: column;
  }
  .form-field-full {
    grid-column: 1 / -1;
  }
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    gap: 16px;
  }
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--iluria-color-border);
    border-top: 3px solid var(--iluria-color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  .loading-text {
    font-size: 16px;
    color: var(--iluria-color-text-secondary);
    margin: 0;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* CEP Loading */
  .zip-code-field {
    position: relative;
  }

  .loading-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 12px;
    color: var(--iluria-color-text-secondary);
  }

  .spinner {
    width: 14px;
    height: 14px;
    border: 2px solid var(--iluria-color-border);
    border-top: 2px solid var(--iluria-color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  /* Responsive */
  @media (max-width: 768px) {
    .store-physical-data-container {
      padding: 16px;
    }
    
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }
    
    .header-actions {
      justify-content: flex-end;
    }
    
    .form-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
  </style> 