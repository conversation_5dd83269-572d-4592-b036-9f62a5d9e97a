import { ref, onUnmounted } from 'vue'
import { useStoreStore } from '@/stores/store.store'
import { useToast } from '@/services/toast.service'

export function useThemeWebSocket() {
  const storeStore = useStoreStore()
  const toast = useToast()
  
  // Estado da conexão WebSocket
  const socket = ref(null)
  const isConnected = ref(false)
  const isConnecting = ref(false)
  
  // Estado do progresso
  const progressData = ref({
    type: '',
    message: '',
    progress: 0,
    timestamp: null
  })
  
  // Callbacks para eventos específicos
  const callbacks = ref({
    onProgress: [],
    onCompleted: [],
    onError: [],
    onConnected: [],
    onDisconnected: [],
    onRestoreCompleted: []
  })
  
  /**
   * Conecta ao WebSocket do tema
   */
  const connect = () => {
    if (isConnected.value || isConnecting.value) {
      return Promise.resolve()
    }
    
    const storeId = storeStore.getSelectedStoreId()
    if (!storeId) {
      return Promise.reject(new Error('Store ID não disponível'))
    }
    
    return new Promise((resolve, reject) => {
      try {
        isConnecting.value = true
        
        // Construir URL do WebSocket nativo
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const host = window.location.hostname
        const port = process.env.NODE_ENV === 'development' ? ':8081' : ''
        const wsUrl = `${protocol}//${host}${port}/ws/theme-progress/${storeId}`


        // Criar conexão WebSocket nativa
        socket.value = new WebSocket(wsUrl)
        
        // Event listeners
        socket.value.onopen = () => {
          isConnected.value = true
          isConnecting.value = false
          
          // Executar callbacks de conexão
          callbacks.value.onConnected.forEach(callback => {
            try { callback() } catch (e) { }
          })
          
          resolve()
        }
        
        socket.value.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            handleProgressMessage(data)
          } catch (e) {
          }
        }
        
        socket.value.onclose = (event) => {
          isConnected.value = false
          isConnecting.value = false
          socket.value = null

          // Executar callbacks de desconexão apenas se não foi fechamento normal
          if (event.code !== 1000) {
            callbacks.value.onDisconnected.forEach(callback => {
              try { callback(event) } catch (e) { }
            })
          }
        }
        
        socket.value.onerror = (error) => {
          isConnecting.value = false
          
          // Executar callbacks de erro
          callbacks.value.onError.forEach(callback => {
            try { callback(error) } catch (e) { }
          })
          
          reject(error)
        }
        
        // Timeout para conexão
        setTimeout(() => {
          if (isConnecting.value) {
            isConnecting.value = false
            reject(new Error('Timeout na conexão WebSocket'))
          }
        }, 10000)
        
      } catch (error) {
        isConnecting.value = false
        reject(error)
      }
    })
  }
  
  /**
   * Desconecta do WebSocket
   */
  const disconnect = () => {
    if (socket.value) {
      isConnected.value = false
      isConnecting.value = false

      try {
        socket.value.close(1000, 'Desconexão manual')
      } catch (e) {
        // Ignorar erros de fechamento
      }
      socket.value = null
    }
  }

  /**
   * Desconecta do WebSocket de forma suave (sem forçar)
   */
  const gracefulDisconnect = () => {
    if (socket.value && isConnected.value) {
      // Marcar como desconectado primeiro para evitar warnings
      isConnected.value = false

      // Aguardar um pouco antes de fechar para permitir que mensagens finais sejam processadas
      setTimeout(() => {
        if (socket.value) {
          try {
            socket.value.close(1000, 'Operação concluída')
          } catch (e) {
            // Ignorar erros de fechamento
          }
          socket.value = null
        }
      }, 500)
    }
  }
  
  /**
   * Processa mensagens de progresso recebidas
   */
  const handleProgressMessage = (data) => {
    progressData.value = {
      type: data.type,
      message: data.message,
      progress: data.progress,
      timestamp: data.timestamp || Date.now()
    }
    
    
    // Executar callbacks de progresso
    callbacks.value.onProgress.forEach(callback => {
      try { callback(data) } catch (e) { console.error('Erro em callback onProgress:', e) }
    })
    
    // Mostrar toast para marcos importantes
    if (data.type === 'THEME_CHANGE_COMPLETED') {
      toast.showSuccess(data.message, { title: 'Tema Aplicado!' })
      
      // Executar callbacks de conclusão
      callbacks.value.onCompleted.forEach(callback => {
        try { callback(data) } catch (e) { console.error('Erro em callback onCompleted:', e) }
      })
      
      // Desconectar após conclusão bem-sucedida de forma suave
      setTimeout(() => gracefulDisconnect(), 2000)
    } else if (data.type === 'THEME_CHANGE_ERROR') {
      toast.showError(data.message, { title: 'Erro ao Aplicar Tema' })
      
      // Executar callbacks de erro
      callbacks.value.onError.forEach(callback => {
        try { callback(data) } catch (e) { console.error('Erro em callback onError:', e) }
      })
      
      // Desconectar após erro de forma suave
      setTimeout(() => gracefulDisconnect(), 3000)
    } else if (data.type === 'THEME_RESTORE_COMPLETED') {
      // Executar callbacks de restauração completada
      callbacks.value.onRestoreCompleted.forEach(callback => {
        try { callback(data) } catch (e) { console.error('Erro em callback onRestoreCompleted:', e) }
      })
    }
  }
  
  /**
   * Adiciona callback para eventos específicos
   */
  const onProgress = (callback) => {
    callbacks.value.onProgress.push(callback)
  }
  
  const onCompleted = (callback) => {
    callbacks.value.onCompleted.push(callback)
  }
  
  const onError = (callback) => {
    callbacks.value.onError.push(callback)
  }
  
  const onConnected = (callback) => {
    callbacks.value.onConnected.push(callback)
  }
  
  const onDisconnected = (callback) => {
    callbacks.value.onDisconnected.push(callback)
  }
  
  const onRestoreCompleted = (callback) => {
    callbacks.value.onRestoreCompleted.push(callback)
  }
  
  /**
   * Conecta automaticamente quando uma operação de tema inicia
   */
  const connectForThemeOperation = async () => {
    try {
      await connect()
      return true
    } catch (error) {
      console.error('Erro ao conectar WebSocket para operação de tema:', error)
      toast.showWarning('Não foi possível conectar ao sistema de progresso em tempo real.', {
        title: 'Aviso'
      })
      return false
    }
  }
  
  // Cleanup na desmontagem do componente
  onUnmounted(() => {
    if (socket.value) {
      isConnected.value = false
      isConnecting.value = false

      try {
        socket.value.close(1000, 'Componente desmontado')
      } catch (e) {
        // Ignorar erros de fechamento
      }
      socket.value = null
    }
  })
  
  return {
    // Estado
    isConnected,
    isConnecting,
    progressData,
    
    // Métodos
    connect,
    disconnect,
    gracefulDisconnect,
    connectForThemeOperation,
    
    // Event listeners
    onProgress,
    onCompleted,
    onError,
    onConnected,
    onDisconnected,
    onRestoreCompleted
  }
}