<template>
    <div class="giftCard-list-container">
        <IluriaHeader :title="t('product.giftCardTitle')" :subtitle="t('product.giftCardSubtitle')" :showSearch="true"
            :showAdd="true" :addText="t('product.giftCardButton')" @search="debouncedSearch" @add-click="addGiftCard" />

        <div class="table-wrapper">
            <IluriaDataTable
                :value="giftCards"
                :columns="tableColumns"
                :loading="loading"
                dataKey="id"
                v-model:expandedRows="expandedRows"
                :rowClass="rowClass"
                class="product-table">

                <template #header-image>
                    <span class="column-header">{{ t('product.image') }}</span>
                </template>
                <template #header-name>
                    <span class="column-header">{{ t('product.name') }}</span>
                </template>
                <template #header-denominations>
                    <span class="column-header">{{ t('product.denominations') }}</span>
                </template>
                <template #header-createdAt>
                    <span class="column-header">{{ t('customer.createdAt') }}</span>
                </template>
                <template #header-actions>
                    <span class="column-header">{{ t('actions') }}</span>
                </template>

                <!-- Slot para renderizar a imagem -->
                <template #column-image="{ data }">
                    <div class="product-image-container">
                        <div class="product-image-wrapper">
                            <img v-if="getProductImage(data)" :src="getProductImage(data)" alt="Product"
                                class="product-image" />
                            <div v-else class="product-image-placeholder">
                                <i class="fas fa-image"></i>
                            </div>
                        </div>
                    </div>
                </template>
                <!-- Slot para renderizar o nome do produto -->
                <template #column-name="{ data }">
                    <div class="product-info">
                        <h3 class="product-name" :title="data.name">
                            {{ truncateText(data.name, 40) }}
                        </h3>
                        <p class="product-description" v-if="data.description" :title="stripHtml(data.description)">
                            {{ truncateText(stripHtml(data.description), 60) }}
                        </p>
                    </div>
                </template>

                <!-- Slot para renderizar as denominações -->
                <template #column-denominations="{ data }">
                    <span v-if="Array.isArray(data.denominations) && data.denominations.length > 0" class="denominations-span">
                        <template v-if="data.denominations.length === 1">
                            {{ formatCurrency(data.denominations[0]) }}
                        </template>
                        <template v-else>
                            {{ formatCurrency(Math.min(...data.denominations)) }} - {{ formatCurrency(Math.max(...data.denominations)) }}
                        </template>
                    </span>
                    <span v-else>
                        -
                    </span>
                </template>

                <!-- Slot para renderizar a data de criação -->
                <template #column-createdAt="{ data }">
                    <p class="info-text">
                        {{ formatDate(data.createdAt) }}
                    </p>
                </template>

                <!-- Slot para renderizar as ações -->
                <template #column-actions="{ data }">
                    <div class="action-buttons">
                        <IluriaButton color="text-primary" size="small" :hugeIcon="PencilEdit01Icon"
                            @click.prevent="editProduct(data.id)" :title="t('product.edit')" />
                        <IluriaButton color="text-danger" size="small" :hugeIcon="Delete01Icon"
                            @click.prevent="confirmDelete(data)" :title="t('product.remove')" />
                    </div>
                </template>
                <template #empty>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-box-open"></i>
                        </div>
                        <h3 class="empty-title">{{ t('product.noProductsFound') }}</h3>
                        <p class="empty-description">Comece criando seu primeiro cartão-presente</p>
                        <IluriaButton @click="addGiftCard" :hugeIcon="PlusSignSquareIcon" class="mt-4">
                            {{ t('addProduct') }}
                        </IluriaButton>
                    </div>
                </template>
                <template #loading>
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <span>{{ t('product.loading') }}</span>
                    </div>
                </template>
            </IluriaDataTable>
        </div>
        <div class="pagination-container" v-if="totalPages > 0">
            <IluriaPagination :current-page="currentPage" :total-pages="totalPages" @go-to-page="changePage" />
        </div>
        <IluriaConfirmationModal
            :is-visible="showConfirmModal"
            :title="confirmModalTitle"
            :message="confirmModalMessage"
            :confirm-text="confirmModalConfirmText"
            :cancel-text="confirmModalCancelText"
            :type="confirmModalType"
            @confirm="handleConfirm"
            @cancel="handleCancel"
        />
    </div>
</template>


<script setup>
import IluriaHeader from '../../components/iluria/IluriaHeader.vue';
import IluriaButton from '../../components/iluria/IluriaButton.vue';
import IluriaPagination from '../../components/iluria/IluriaPagination.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaDataTable from '../../components/iluria/IluriaDataTable.vue';
import { productsApi } from '@/services/product.service'
import { useI18n } from 'vue-i18n'
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from '@/services/toast.service';

import { PlusSignSquareIcon, PencilEdit01Icon, Delete01Icon } from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const router = useRouter()
const toast = useToast()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const giftCards = ref([])
const expandedRows = ref([])
const loading = ref(true)

// Definição das colunas da tabela
const tableColumns = computed(() => [
    { field: 'image', headerClass: 'col-image', class: 'col-image' },
    { field: 'name', headerClass: 'col-flex', class: 'col-flex' },
    { field: 'denominations', headerClass: 'col-denominations', class: 'col-denominations' },
    { field: 'createdAt', headerClass: 'col-created', class: 'col-created' },
    { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
])
const currentPage = ref(0)
const totalPages = ref(0)
const totalElements = ref(0)
const filters = ref({
    name: '',
    onlyInStock: false
})

onMounted(() => {
    loadGiftCards()

})

const loadGiftCards = async () => {
    loading.value = true
    try {
        const params = {
            page: currentPage.value,
            size: 10,
            name: filters.value.name,
        }

        const response = await productsApi.getGiftCards(params)
        giftCards.value = response.data.content || response.data
        totalPages.value = response.data.page?.totalPages || response.data.totalPages
        totalElements.value = response.data.page?.totalElements || response.data.totalElements
      
    } catch (error) {
        console.error('Error loading gift cards:', error)
    } finally {
        loading.value = false
    }
}

let searchTimeout = null
const debouncedSearch = () => {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
        currentPage.value = 0
        loadGiftCards()
    }, 400)
}

const rowClass = (data) => {
    return {
        'has-variation': hasVariations(data)
    };
};

const hasVariations = (giftCards) => {
    return giftCards.denominations && giftCards.denominations.length > 0
}

const addGiftCard = () => {
    router.push('/products/gift-card/new')
}

const truncateText = (text, maxLength) => {
    if (!text) return ''
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
}

const stripHtml = (html) => {
    if (!html) return ''
    const tmp = document.createElement('div')
    tmp.innerHTML = html
    return tmp.textContent || tmp.innerText || ''
}

const changePage = (page) => {
    currentPage.value = page
    loadGiftCards()
}

const formatCurrency = (value) => {
    if (typeof value !== 'number') return value;
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
    }).format(value);
};

const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('pt-BR');
};

const editProduct = (id) => {
    router.push(`/products/gift-card/${id}`)
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
    confirmModalMessage.value = message
    confirmModalTitle.value = title
    confirmModalConfirmText.value = t('delete')
    confirmModalCancelText.value = t('cancel')
    confirmModalType.value = 'error'
    confirmCallback.value = onConfirm
    showConfirmModal.value = true
}

const handleConfirm = () => {
    if (confirmCallback.value) {
        confirmCallback.value()
    }
    showConfirmModal.value = false
}

const handleCancel = () => {
    showConfirmModal.value = false
}

const confirmDelete = (giftCard) => {
    showConfirmDanger(
        t('product.confirmDelete', { name: giftCard.name }),
        t('product.confirmDeleteTitle'),
        () => deleteProduct(giftCard.id)
    )
}

const getProductImage = (giftCard) => {
    if (giftCard.photos && giftCard.photos.length > 0) {
        const photo = giftCard.photos.find(p => p.position === 0) || giftCard.photos[0];
        if (photo && photo.url) {
            return photo.url;
        }
    }
    return giftCard.imageUrl || null;
};

const deleteProduct = async (id) => {
    try {
        await productsApi.deleteGiftCard(id)
        loadGiftCards()
        toast.showSuccess(t('product.giftCardExcludedSuccess'))
    } catch (error) {
        toast.showError(t('product.giftCardExcludedFail'))
    }
}

</script>

<style scoped>
.giftCard-list-container {
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

.table-wrapper {
    margin-bottom: 24px;
    width: 100%;
    overflow-x: auto;
}



/* Header spacing */
:deep(.iluria-header) {
    margin-bottom: 0;
    width: 100%;
}



/* Column definitions - seguindo padrão do ProductListView */
:deep(.col-image) { width: 80px; text-align: center; }
:deep(.col-flex) { width: auto; text-align: left; }
:deep(.col-denominations) { width: 160px; text-align: center; }
:deep(.col-created) { width: 140px; text-align: center; }
:deep(.col-actions) { width: 120px; text-align: center; }

/* General table styling */
:deep(.product-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.product-table .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.product-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: center;
}

:deep(.product-table .p-datatable-thead > tr > th.col-flex) {
  text-align: left;
}

:deep(.product-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.product-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.product-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  text-align: center;
}

:deep(.product-table .p-datatable-tbody > tr > td.col-flex) {
  text-align: left;
}

/* Column header style */
:deep(.product-table th .column-header) {
  display: block;
  width: 100%;
  text-align: inherit;
  cursor: default;
  user-select: none;
}

:deep(.product-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}




.product-price {
    font-size: 14px;
    font-weight: 600;
    color: #059669;
}

.action-buttons {
    display: flex;
    gap: 6px;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
}

/* Estilos para denominações - sem container */
.denominations-span {
    display: inline;
    font-size: 14px;
    font-weight: 500;
    color: var(--iluria-color-text-primary);
    white-space: nowrap;
}

/* Estilos para data */
.info-text {
    font-size: 14px;
    color: var(--iluria-color-text-secondary);
    margin: 0;
    line-height: 1.4;
    text-align: center;
    white-space: nowrap;
}

.empty-state {
    text-align: center;
    padding: 48px 24px;
    background: var(--iluria-color-surface);
    transition: background-color 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.empty-icon {
    font-size: 48px;
    color: var(--iluria-color-text-muted);
    margin-bottom: 16px;
    transition: color 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.empty-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 8px 0;
    transition: color 0.3s ease;
    text-align: center;
}

.empty-description {
    font-size: 14px;
    color: var(--iluria-color-text-secondary);
    margin: 0 0 16px 0;
    transition: color 0.3s ease;
    text-align: center;
    line-height: 1.5;
    max-width: 400px;
}

/* Loading State */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 32px;
    color: var(--iluria-color-text-secondary);
    font-size: 14px;
    background: var(--iluria-color-surface);
    transition: all 0.3s ease;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.product-info {
    display: block;
    text-align: left;
    width: 100%;
    padding: 0;
    margin: 0;
}

/* Centralização perfeita para imagens */
:deep(.product-image-container) {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Wrapper para garantir tamanho consistente das imagens */
:deep(.product-image-wrapper) {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--iluria-color-sidebar-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--iluria-color-border);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

/* Estilos específicos para imagem */
:deep(.product-image) {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

/* Placeholder específico */
:deep(.product-image-placeholder) {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--iluria-color-text-muted);
    font-size: 18px;
    transition: color 0.3s ease;
}

/* Hover effects para imagens */
:deep(.product-image-wrapper:hover .product-image) {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.1) saturate(1.1);
}

.product-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 4px 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
    transition: color 0.3s ease;
    text-align: left;
    display: block;
}

.product-description {
    font-size: 13px;
    color: var(--iluria-color-text-secondary);
    margin: 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
    transition: color 0.3s ease;
    text-align: left;
    display: block;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    width: 100%;
}



@media (max-width: 1024px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }

    .header-actions {
        justify-content: space-between;
    }

    .search-input {
        min-width: 200px;
    }
}

@media (max-width: 768px) {
    .giftCard-list-container {
        padding: 16px;
    }

    /* Filtros de imagem responsivos */
    .image-filters-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
    }

    .filter-label {
        margin-top: 0;
        margin-bottom: 4px;
    }

    .filter-buttons {
        width: 100%;
        justify-content: flex-start;
    }

    .filter-btn {
        padding: 6px 12px;
        font-size: 12px;
        height: 32px;
        min-width: 60px;
    }

    /* Mobile truncation adjustments */
    .product-name {
        font-size: 13px;
    }

    .product-description {
        font-size: 12px;
    }
}


</style>