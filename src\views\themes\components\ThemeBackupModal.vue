<template>
  <IluriaModal
    v-model="showModal"
    :title="modalTitle"
    max-width="900px"
    @close="$emit('close')"
  >
    <div class="theme-backup-modal">
      <!-- Backup History Component -->
      <ThemeBackupHistory :theme="theme" @theme-restored="handleThemeRestored" />
    </div>
    
    
  </IluriaModal>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import ThemeBackupHistory from './ThemeBackupHistory.vue'

const { t } = useI18n()

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  theme: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'theme-restored'])

// Computed
const showModal = computed({
  get: () => props.show,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})

const modalTitle = computed(() => {
 
  if (props.theme) {
    const title = t('themes.backup.historyOf', { themeName: props.theme.name })
    return title
  }
  const title = t('themes.backup.history')

  return title
})

// Methods
const handleThemeRestored = () => {
  emit('theme-restored')
}
</script>

<style scoped>
.theme-backup-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
</style>