<template>
  <IluriaModal
    v-model="visible"
    :title="t('team.inviteMember.title')"
    :subtitle="t('team.inviteMember.subtitle')"
    :icon="UserAdd01Icon"
    :saveLabel="t('team.inviteMember.saveLabel')"
    @save="handleSendInvite"
    :saveLocked="!isFormValid || sendingInvite"
    :saveLoading="sendingInvite"
  >
    <div class="invite-form">
      <!-- Email Field -->
      <div class="form-group">
        <IluriaInputText
          v-model="inviteForm.email"
          :label="t('team.inviteMember.form.email.label')"
          :placeholder="t('team.inviteMember.form.email.placeholder')"
          type="email"
          :error="emailError"
          :disabled="sendingInvite"
          @blur="validateEmail"
          required
        />
        <div v-if="emailValidationMessage" :class="emailValidationClass">
          <HugeiconsIcon :icon="emailValidationIcon" size="16" :strokeWidth="1.5" />
          <span>{{ emailValidationMessage }}</span>
        </div>
      </div>

      <!-- Role Selection -->
      <div class="form-group">
        <IluriaSelect
          v-model="inviteForm.role"
          :label="t('team.inviteMember.form.role.label')"
          :placeholder="loadingRoles ? t('team.inviteMember.form.role.loadingPlaceholder') : t('team.inviteMember.form.role.placeholder')"
          :options="roleOptions"
          :disabled="sendingInvite || loadingRoles"
          :error="roleError"
          required
        />
      </div>

      <!-- Optional Message -->
      <div class="form-group">
        <label class="form-label">{{ t('team.inviteMember.form.message.label') }}</label>
        <textarea
          v-model="inviteForm.message"
          :placeholder="t('team.inviteMember.form.message.placeholder')"
          class="invite-message"
          :disabled="sendingInvite"
          rows="3"
          maxlength="500"
        ></textarea>
        <div class="character-count">
          {{ t('team.inviteMember.form.message.characterCount', { current: inviteForm.message.length, max: 500 }) }}
        </div>
      </div>
    </div>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import { 
  UserAdd01Icon,
  CheckmarkCircle01Icon,
  Cancel01Icon,
  AlertCircleIcon
} from '@hugeicons-pro/core-stroke-rounded'
import { teamApi } from '@/services/team.service'
import { roleApi } from '@/services/role.service'
import { useToast } from '@/services/toast.service'
import { useStoreStore } from '@/stores/store.store'

const emit = defineEmits(['member-invited'])
const toast = useToast()
const storeStore = useStoreStore()
const { t } = useI18n()

// Modal state
const visible = ref(false)
const sendingInvite = ref(false)
const emailChecking = ref(false)
const emailExists = ref(null)
const loadingRoles = ref(false)

// Form data
const inviteForm = reactive({
  email: '',
  role: '',
  message: ''
})

// Form validation
const emailError = ref('')
const roleError = ref('')

// Role options - loaded dynamically from API
const roleOptions = ref([])

// Computed properties
const isFormValid = computed(() => {
  return inviteForm.email && 
         inviteForm.role && 
         !emailError.value && 
         !roleError.value &&
         emailExists.value === true
})

const emailValidationMessage = computed(() => {
  if (emailChecking.value) {
    return t('team.inviteMember.validation.checking')
  }

  if (emailExists.value === true) {
    return t('team.inviteMember.validation.found')
  }

  if (emailExists.value === false) {
    return t('team.inviteMember.validation.notFound')
  }

  return ''
})

const emailValidationClass = computed(() => {
  if (emailChecking.value) {
    return 'email-validation email-validation-checking'
  }
  
  if (emailExists.value === true) {
    return 'email-validation email-validation-success'
  }
  
  if (emailExists.value === false) {
    return 'email-validation email-validation-error'
  }
  
  return ''
})

const emailValidationIcon = computed(() => {
  if (emailChecking.value) {
    return AlertCircleIcon
  }
  
  if (emailExists.value === true) {
    return CheckmarkCircle01Icon
  }
  
  if (emailExists.value === false) {
    return Cancel01Icon
  }
  
  return AlertCircleIcon
})

// Helper function to identify system roles by name
const isSystemRole = (roleName) => {
  const systemRoleNames = ['Proprietário', 'Owner', 'Sistema', 'System']
  return systemRoleNames.includes(roleName)
}

// Methods
const loadRoles = async () => {
  loadingRoles.value = true
  try {
    // Load available roles from team API
    const response = await teamApi.getAvailableRoles()
    const roles = response.data || []
    
    // Convert roles to select options format, filtering out system roles by name
    roleOptions.value = roles
      .filter(role => !isSystemRole(role.name))
      .map(role => ({
        value: role.id,   // Use the role ID as the value for backend
        label: role.name  // Display the role name
      }))
    
  } catch (error) {
    console.error('Error loading roles:', error)
    toast.showError(t('team.inviteMember.messages.loadRolesError'))

    // Clear options on error - force user to refresh
    roleOptions.value = []
  } finally {
    loadingRoles.value = false
  }
}

const open = async () => {
  resetForm()
  visible.value = true
  await loadRoles()
}

const close = () => {
  visible.value = false
}

const resetForm = () => {
  inviteForm.email = ''
  inviteForm.role = ''
  inviteForm.message = ''
  emailError.value = ''
  roleError.value = ''
  emailExists.value = null
  sendingInvite.value = false
  emailChecking.value = false
}

const validateEmail = async () => {
  if (!inviteForm.email) {
    emailError.value = t('team.inviteMember.form.email.required')
    emailExists.value = null
    return
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(inviteForm.email)) {
    emailError.value = t('team.inviteMember.form.email.invalid')
    emailExists.value = null
    return
  }

  emailError.value = ''
  
  // Check if email exists in the system
  try {
    emailChecking.value = true
    emailExists.value = null
    
    // Call API to check if user exists
    const response = await teamApi.checkUserExists(inviteForm.email)
    emailExists.value = response.data.exists
    
  } catch (error) {
    console.error('Error checking email:', error)
    // On error, assume email doesn't exist
    emailExists.value = false
  } finally {
    emailChecking.value = false
  }
}

const validateRole = () => {
  if (!inviteForm.role) {
    roleError.value = t('team.inviteMember.form.role.required')
    return false
  }

  roleError.value = ''
  return true
}

const handleSendInvite = async () => {
  // Validate form
  await validateEmail()
  const roleValid = validateRole()
  
  if (!isFormValid.value || !roleValid) {
    return
  }
  
  try {
    sendingInvite.value = true
    
    const inviteData = {
      email: inviteForm.email,
      roleId: inviteForm.role, // Now this contains the role ID
      message: inviteForm.message.trim() || null
    }
    
    
    await teamApi.inviteMember(inviteData)

    toast.showSuccess(t('team.inviteMember.messages.sendSuccess'))

    emit('member-invited')
    close()

  } catch (error) {
    console.error('Error sending invite:', error)
    console.error('Error response:', error.response)
    console.error('Error data:', error.response?.data)

    if (error.response?.data?.message) {
      toast.showError(error.response.data.message)
    } else {
      toast.showError(t('team.inviteMember.messages.sendError'))
    }
  } finally {
    sendingInvite.value = false
  }
}

// Watch for email changes to reset validation
watch(() => inviteForm.email, () => {
  if (emailExists.value !== null) {
    emailExists.value = null
  }
})

// Expose methods for parent component
defineExpose({
  open,
  close
})
</script>

<style scoped>
.invite-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 500px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 4px;
}

.invite-message {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background: var(--iluria-color-surface);
  color: var(--iluria-color-text-primary);
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.invite-message:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.invite-message:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--iluria-color-disabled);
}

.invite-message::placeholder {
  color: var(--iluria-color-text-muted);
}

.character-count {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  text-align: right;
  margin-top: 4px;
}

/* Email Validation Styles */
.email-validation {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 500;
  margin-top: 4px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.email-validation-checking {
  background: #fef3c7;
  color: #d97706;
  border: 1px solid #fbbf24;
}

.email-validation-success {
  background: #d1fae5;
  color: #059669;
  border: 1px solid #34d399;
}

.email-validation-error {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #f87171;
}

/* Responsive */
@media (max-width: 768px) {
  .invite-form {
    max-width: 100%;
  }
  
  .invite-message {
    min-height: 60px;
  }
}

/* Dark theme adjustments */
.theme-dark .invite-message {
  background: #1a1a1a;
  border-color: #333333;
  color: #ffffff;
}

.theme-dark .invite-message:focus {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.theme-dark .email-validation-checking {
  background: rgba(217, 119, 6, 0.2);
  border-color: rgba(251, 191, 36, 0.3);
}

.theme-dark .email-validation-success {
  background: rgba(5, 150, 105, 0.2);
  border-color: rgba(52, 211, 153, 0.3);
}

.theme-dark .email-validation-error {
  background: rgba(220, 38, 38, 0.2);
  border-color: rgba(248, 113, 113, 0.3);
}
</style>