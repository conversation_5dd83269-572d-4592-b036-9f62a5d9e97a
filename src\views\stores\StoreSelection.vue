<template>
  <div class="store-selection-container">
    <!-- User NavBar -->
    <UserNavBar type="user" />

    <!-- Main Content -->
    <div class="main-content">
      <div class="content-wrapper">
        <div class="welcome-section">
          <h1 class="welcome-title">{{ $t('stores.welcomeBack') }}</h1>
          
          <!-- Create Store Button -->
          <Button
            :label="$t('stores.createStore')"
            class="create-store-btn"
            @click="createStore"
            icon="pi pi-chevron-right"
            icon-pos="right"
          >
            <template #icon>
              <HugeiconsIcon :icon="PlusSignIcon" :size="18" />
            </template>
          </Button>
        </div>

        <!-- Stores List -->
        <div class="stores-section">
          <div v-if="loading" class="loading-state">
            <IluriaSpinner size="large" variant="primary" />
            <p>{{ $t('stores.loadingStores') }}</p>
          </div>
          
          <div v-else-if="stores.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="pi pi-shop"></i>
            </div>
            <h3>{{ $t('stores.noStores') }}</h3>
            <p>{{ $t('stores.noStoresDescription') }}</p>
            <Button
              :label="$t('stores.createFirstStore')"
              class="create-store-btn"
              @click="createStore"
              icon="pi pi-chevron-right"
              icon-pos="right"
            >
              <template #icon>
                <HugeiconsIcon :icon="PlusSignIcon" :size="18" />
              </template>
            </Button>
          </div>
          
          <div v-else class="stores-grid">
            <div
              v-for="store in stores"
              :key="store.id || store.name"
              class="store-card"
              @click="selectStore(store)"
            >
              <div class="store-avatar">
                <img
                  v-if="getUserProfileImage(store)"
                  :src="getUserProfileImage(store)"
                  alt="Store Avatar"
                  class="store-avatar-image"
                />
                <span v-else class="store-avatar-initials">
                  {{ getStoreInitials(store?.name) }}
                </span>
              </div>
              <div class="store-info">
                <h3 class="store-name">{{ store?.name || 'Loja sem nome' }}</h3>
                <p class="store-url">{{ store?.urlIluria || store?.url || 'URL não definida' }}</p>
              </div>
              <div class="store-actions">
                <IluriaSpinner v-if="loadingStoreId === store.id" size="medium" variant="muted" />
                <HugeiconsIcon v-else :icon="ArrowRight01Icon" :size="22" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Store Dialog -->
    <IluriaModal
      v-model="showCreateStoreDialog"
      :title="$t('stores.createNewStore')"
      :save-label="$t('stores.createStore')"
      :cancel-label="$t('common.cancel')"
      :dialog-style="{ width: '500px' }"
      :loading="creatingStore"
      @save="handleCreateStore"
      @cancel="showCreateStoreDialog = false"
    >
      <div class="create-store-form">
        <div class="field">
          <IluriaInputText
            id="storeName"
            :label="$t('stores.storeName')"
            v-model="newStore.name"
            :placeholder="$t('stores.storeNamePlaceholder')"
            :disabled="creatingStore"
          />
        </div>
      </div>
    </IluriaModal>

    <!-- Store Initialization Progress -->
    <StoreInitializationProgress
      v-if="isCreatingNewStore && initializationProgress.isInitializing.value && createdStoreName"
      :store-name="createdStoreName"
      :progress="initializationProgress.progress.value"
      :error="initializationProgress.error.value"
      @retry="retryInitialization"
      @completed="handleInitializationCompleted"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth.store'
import { useStoreStore } from '@/stores/store.store'
import { resetGlobalPermissions } from '@/composables/usePermissions'
import storeService from '@/services/store.service'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import { HugeiconsIcon } from '@hugeicons/vue'
import { PlusSignIcon, ArrowRight01Icon } from '@hugeicons-pro/core-stroke-rounded'
import UserNavBar from '@/components/layout/UserNavBar.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaSpinner from '@/components/iluria/IluriaSpinner.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import StoreInitializationProgress from '@/components/stores/StoreInitializationProgress.vue'
import { useStoreInitializationProgress } from '@/components/stores/useStoreInitializationProgress'

const { t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()
const storeStore = useStoreStore()
const toast = useToast()

// Refs
const showCreateStoreDialog = ref(false)
const creatingStore = ref(false)
const createdStoreName = ref('')
const isCreatingNewStore = ref(false)
const loadingStoreId = ref(null)
const newStore = ref({
  name: ''
})

// Store initialization progress
const initializationProgress = useStoreInitializationProgress()

// Computed from store
const loading = computed(() => storeStore.loading)
const stores = computed(() => storeStore.userStores)

// Methods

const createStore = () => {
  showCreateStoreDialog.value = true
  newStore.value = { name: '' }
}
const getUserProfileImage = (user) => {
    if (user.url) {
        return user.url;
    }
    //return generateCustomerAvatar(user.fullName || user.firstName);
};

const generateStoreUrl = (storeName) => {
  if (!storeName) return 'nome-da-loja.iluria.com'
  const slug = storeName
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
  return `${slug || 'minha-loja'}.iluria.com`
}

const handleCreateStore = async () => {
  if (!newStore.value.name.trim()) {
    toast.add({
      severity: 'error',
      summary: t('common.error'),
      detail: t('stores.storeNameRequired'),
      life: 3000
    })
    return
  }

  creatingStore.value = true
  createdStoreName.value = newStore.value.name
  isCreatingNewStore.value = true

  try {
    // Start listening for initialization progress
    const userEmail = authStore.user?.email || '<EMAIL>' // fallback
    
    // Set initializing to true immediately to show the progress screen
    initializationProgress.isInitializing.value = true
    
    await initializationProgress.startListening(userEmail)

    const createdStore = await storeStore.createStore(newStore.value)

    toast.add({
      severity: 'success',
      summary: t('success'),
      detail: t('stores.storeCreatedSuccess'),
      life: 3000
    })

    showCreateStoreDialog.value = false
    // Store is automatically added to the list by the store
  } catch (error) {
    console.error('Error creating store:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: t('stores.storeCreationError'),
      life: 3000
    })
    initializationProgress.disconnect()
    isCreatingNewStore.value = false
  } finally {
    creatingStore.value = false
  }
}

const selectStore = async (store) => {
  loadingStoreId.value = store.id
  
  try {
    // Reset global permissions before switching stores
    resetGlobalPermissions()
    
    // Clear any existing store token before authenticating new store
    authStore.clearStoreToken()
    
    const authData = await storeService.authenticateStore(store.id)
    
    // Set store token for store-specific operations
    authStore.setStoreToken(authData.jwt || authData.token)
    
    storeStore.setSelectedStore(store)

    toast.add({
      severity: 'success',
      summary: t('stores.enteringStore'),
      detail: `${t('stores.enteringStore')} ${store.name}`,
      life: 2000
    })

    setTimeout(() => {
      router.push('/')
    }, 1000)
  } catch (error) {
    console.error('Error authenticating store:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error'),
      detail: t('stores.storeAccessError'),
      life: 3000
    })
    loadingStoreId.value = null
  }
}

const getStoreInitials = (storeName) => {
  if (!storeName || typeof storeName !== 'string') {
    return 'ST'
  }
  return storeName
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2) || 'ST'
}

const retryInitialization = () => {
  initializationProgress.reset()
  initializationProgress.disconnect()
  isCreatingNewStore.value = false

  toast.add({
    severity: 'info',
    summary: t('common.info'),
    detail: 'Funcionalidade de retry será implementada em breve',
    life: 3000
  })
}

const handleInitializationCompleted = async () => {
  // Find the created store
  const createdStore = stores.value.find(store => store.name === createdStoreName.value)
  
  if (createdStore) {
    try {
      // Authenticate with the created store
      authStore.clearStoreToken()
      const authData = await storeService.authenticateStore(createdStore.id)
      authStore.setStoreToken(authData.jwt || authData.token)
      storeStore.setSelectedStore(createdStore)

      // Show success message
      toast.add({
        severity: 'success',
        summary: t('stores.initializationCompleted'),
        detail: `${t('stores.enteringStore')} ${createdStore.name}`,
        life: 2000
      })

      // Redirect to the store dashboard
      setTimeout(() => {
        router.push('/')
      }, 1000)
    } catch (error) {
      console.error('Error authenticating with created store:', error)
      toast.add({
        severity: 'error',
        summary: t('common.error'),
        detail: t('stores.storeAccessError'),
        life: 3000
      })
    }
  }
  
  // Clean up
  initializationProgress.disconnect()
  isCreatingNewStore.value = false
  // Set initializing to false to hide the progress component
  initializationProgress.isInitializing.value = false
}

const loadStores = async () => {
  try {
    await storeStore.loadUserStores()
  } catch (error) {
    console.error('Error loading stores:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error'),
      detail: t('stores.loadStoresError'),
      life: 3000
    })
  }
}

// Watchers
watch(() => initializationProgress.progress.value.step, (newStep, oldStep) => {
  if (newStep === 'COMPLETED' && oldStep !== newStep) {
    // Reload stores to show the new store
    loadStores()
  }
}, { immediate: false })

// Lifecycle
onMounted(() => {
  loadStores()
})
</script>

<style scoped>
.store-selection-container {
  min-height: 100vh;
  background-color: var(--iluria-color-body-bg);
  background-image: url("@/assets/img/login/login-splash-4.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  flex-direction: column;
  position: relative;
}

.store-selection-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 1rem;
  min-height: calc(100vh - 80px);
  position: relative;
  z-index: 2;
}

.content-wrapper {
  background: var(--iluria-color-container-bg);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
              0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 1px solid var(--iluria-color-border);
  max-width: 520px;
  width: 100%;
  min-height: 420px;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--iluria-color-border);
  position: relative;
  gap: 1rem;
}

.welcome-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--iluria-color-text-primary);
  margin: 0;
  letter-spacing: -0.025em;
  line-height: 1.2;
  display: flex;
  align-items: center;
}

.create-store-btn {
  background: var(--iluria-color-button-primary-bg);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.25rem;
  color: var(--iluria-color-button-primary-fg);
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: fit-content;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-store-btn:hover {
  background: var(--iluria-color-button-primary-bg-hover);
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.stores-section {
  min-height: 400px;
  max-height: 65vh;
  overflow-y: auto;
  padding: 4px 0 0 0;
  margin-top: 0;
}

/* Estilização da scrollbar */
.stores-section::-webkit-scrollbar {
  width: 8px;
}

.stores-section::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.stores-section::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.stores-section::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-text-secondary);
  background-clip: content-box;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.25rem;
  text-align: center;
  height: 350px;
}



/* Animações do Iluria Design System */
@keyframes iluria-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animação suave para o chevron/arrow */
@keyframes iluria-arrow-bounce {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(2px);
  }
}

.empty-icon {
  font-size: 5rem;
  color: var(--iluria-color-text-muted);
  margin-bottom: 2rem;
  opacity: 0.6;
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.75rem 0;
}

.empty-state p {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
  max-width: 350px;
}

.stores-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-bottom: 1rem;
  padding-top: 0;
}

.store-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--iluria-color-container-bg);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  min-height: 88px;
  gap: 1.25rem;
}

.store-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(var(--iluria-color-primary-rgb, 31, 41, 55), 0.03) 0%,
    transparent 50%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.store-card:hover {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-4px);
}

.store-card:hover::before {
  opacity: 1;
}

.store-card:active {
  transform: translateY(-2px);
  transition: transform 0.1s ease;
}

.store-avatar {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--iluria-color-primary) 0%, var(--iluria-color-primary-hover) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1),
              0 4px 6px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 1;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.store-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}

.store-avatar-initials {
  color: var(--iluria-color-primary-contrast);
  font-weight: 800;
  font-size: 1.25rem;
  letter-spacing: -0.025em;
}

.store-info {
  flex: 1;
  position: relative;
  z-index: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.store-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.125rem 0;
  line-height: 1.3;
  letter-spacing: -0.025em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-url {
  color: var(--iluria-color-text-secondary);
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.75;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 14px;
  background: var(--iluria-color-hover);
  color: var(--iluria-color-primary);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  flex-shrink: 0;
}

.store-actions i,
.store-actions svg {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* Store actions hover states para o IluriaSpinner */
.store-card:hover .store-actions :deep(.iluria-spinner .spinner-ring) {
  border-top-color: var(--iluria-color-primary-contrast);
}

.store-card:hover .store-actions :deep(.iluria-spinner .spinner-ring::before) {
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    var(--iluria-color-primary-contrast) 90deg,
    transparent 180deg
  );
  opacity: 0.2;
}

.store-card:hover .store-actions {
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast);
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1),
              0 4px 6px -1px rgba(0, 0, 0, 0.06);
  transform: scale(1.05);
}

.store-card:hover .store-actions i,
.store-card:hover .store-actions svg {
  color: var(--iluria-color-primary-contrast);
  animation: iluria-arrow-bounce 1.2s ease-in-out infinite;
}




/* Create Store Dialog */

.create-store-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field label {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.field-help {
  color: var(--iluria-color-text-secondary);
  font-size: 0.75rem;
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
  .main-content {
    padding: 1.5rem 1rem;
  }

  .content-wrapper {
    padding: 2rem;
    border-radius: 20px;
    max-width: 100%;
  }

  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding-bottom: 1.5rem;
  }

  .welcome-title {
    font-size: 1.375rem;
  }

  .stores-section {
    max-height: 60vh;
    padding-right: 8px;
  }

  .stores-section::-webkit-scrollbar {
    width: 6px;
  }

  .store-card {
    padding: 1.25rem;
    border-radius: 16px;
  }

  .store-avatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
  }

  .store-avatar-initials {
    font-size: 1.125rem;
  }

  .store-name {
    font-size: 1rem;
  }

  .store-url {
    font-size: 0.8125rem;
  }

  .store-actions {
    width: 40px;
    height: 40px;
    border-radius: 12px;
  }

  .store-actions i,
  .store-actions svg {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 1rem 0.75rem;
  }

  .content-wrapper {
    padding: 1.5rem;
    border-radius: 16px;
    min-height: 360px;
  }

  .welcome-title {
    font-size: 1.25rem;
  }

  .stores-section {
    max-height: 55vh;
    min-height: 320px;
  }

  .store-card {
    padding: 1rem;
    min-height: 76px;
    gap: 0.875rem;
  }

  .store-avatar {
    width: 44px;
    height: 44px;
    border-radius: 10px;
  }

  .store-avatar-initials {
    font-size: 1rem;
  }

  .store-name {
    font-size: 0.9375rem;
  }

  .store-url {
    font-size: 0.75rem;
  }

  .store-actions {
    width: 36px;
    height: 36px;
    border-radius: 10px;
  }

  .store-actions i,
  .store-actions svg {
    font-size: 1rem;
  }
}
</style>
