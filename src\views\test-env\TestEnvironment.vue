<template>
  <div class="test-environment">
    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">{{ $t('themes.loadingPreview') }}</p>
    </div>

    <!-- Error state -->
    <div v-else-if="hasError" class="error-container">
      <div class="error-content">
        <h2 class="error-title">{{ $t('themes.previewError') }}</h2>
        <p class="error-message">{{ errorMessage }}</p>
        <div class="error-actions">
          <IluriaButton 
            variant="outline" 
            color="primary" 
            @click="retryPreview"
          >
            {{ $t('themes.retry') }}
          </IluriaButton>
          <IluriaButton 
            variant="solid" 
            color="primary" 
            @click="goBackToThemes"
          >
            {{ $t('themes.backToThemes') }}
          </IluriaButton>
        </div>
      </div>
    </div>

    <!-- Store preview iframe -->
    <iframe 
      v-else
      ref="storeIframe"
      :src="blobUrl"
      class="store-iframe"
      title="Store Preview"
      @load="handleIframeLoad"
      @error="handleIframeError"
    />

    <!-- Back to themes button -->
    <button 
      v-if="!isLoading && !hasError"
      @click="goBackToThemes" 
      class="back-button"
      :title="$t('themes.backToThemes')"
    >
      <HugeiconsIcon :icon="ArrowLeft01Icon" size="20" />
      {{ $t('themes.backToThemes') }}
    </button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStoreStore } from '@/stores/store.store'
import { useAuthStore } from '@/stores/auth.store'
import { useI18n } from 'vue-i18n'
import { ArrowLeft01Icon } from '@hugeicons-pro/core-stroke-standard'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { HugeiconsIcon } from '@hugeicons/vue'

const { t } = useI18n()
const router = useRouter()
const storeStore = useStoreStore()
const authStore = useAuthStore()

const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')
const storeIframe = ref(null)
const blobUrl = ref(null)

// Computed property for the preview URL
const previewUrl = computed(() => {
  const storeId = storeStore.getSelectedStoreId()
  if (storeId && storeId !== 'unknown') {
    // Get current theme from store
    const currentTheme = storeStore.selectedStore?.currentThemeId
    const themeParam = currentTheme ? `?theme=${currentTheme}` : ''
    return `http://localhost:8081/dev-env/${storeId}${themeParam}`
  }
  return null
})

// Function to rewrite URLs in HTML content
const rewriteHtmlContent = (html, baseUrl) => {
  // Create a parser to handle the HTML
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  // Add base tag for relative URLs
  const base = doc.createElement('base')
  base.href = baseUrl
  doc.head.prepend(base)
  
  // Rewrite all relative links to be absolute
  const elements = doc.querySelectorAll('[src], [href]')
  elements.forEach(el => {
    const attr = el.hasAttribute('src') ? 'src' : 'href'
    const url = el.getAttribute(attr)
    if (url && !url.startsWith('http') && !url.startsWith('//') && !url.startsWith('#')) {
      // Convert relative URL to absolute
      if (url.startsWith('/')) {
        el.setAttribute(attr, `http://localhost:8081${url}`)
      } else {
        el.setAttribute(attr, `${baseUrl}/${url}`)
      }
    }
  })
  
  return doc.documentElement.outerHTML
}

// Function to fetch content with authentication
const fetchStoreContent = async () => {
  try {
    const storeId = storeStore.getSelectedStoreId()
    if (!storeId || storeId === 'unknown') {
      throw new Error('No store selected')
    }

    // Construct URL with theme parameter
    const currentTheme = storeStore.selectedStore?.currentThemeId
    const themeParam = currentTheme ? `?theme=${currentTheme}` : ''
    const url = `http://localhost:8081/dev-env/${storeId}${themeParam}`
    
    // Fetch with authentication headers
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authStore.storeToken}`,
        'X-Store-Id': storeId,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('text/html')) {
      throw new Error('Invalid content type received')
    }

    const htmlContent = await response.text()
    
    // Rewrite HTML content to fix relative URLs
    const rewrittenHtml = rewriteHtmlContent(htmlContent, `http://localhost:8081/dev-env/${storeId}`)
    
    // Create blob URL for iframe
    const blob = new Blob([rewrittenHtml], { type: 'text/html' })
    if (blobUrl.value) {
      URL.revokeObjectURL(blobUrl.value)
    }
    blobUrl.value = URL.createObjectURL(blob)
    
    isLoading.value = false
    hasError.value = false
    
  } catch (error) {
    console.error('Error fetching store content:', error)
    hasError.value = true
    errorMessage.value = error.message.includes('No store selected') 
      ? t('themes.noStoreSelected') 
      : error.message.includes('HTTP error') 
      ? t('themes.backendNotAvailable')
      : t('themes.previewErrorMessage')
    isLoading.value = false
  }
}

const handleIframeLoad = () => {
  // Iframe loaded successfully
  isLoading.value = false
  hasError.value = false
}

const handleIframeError = () => {
  // Iframe failed to load
  isLoading.value = false
  hasError.value = true
  errorMessage.value = t('themes.previewErrorMessage')
}

const goBackToThemes = () => {
  router.push('/themes')
}

const retryPreview = async () => {
  hasError.value = false
  isLoading.value = true
  errorMessage.value = ''
  
  await fetchStoreContent()
}

onMounted(async () => {
  await fetchStoreContent()
})

onUnmounted(() => {
  // Clean up blob URL
  if (blobUrl.value) {
    URL.revokeObjectURL(blobUrl.value)
  }
})
</script>

<style scoped>
.test-environment {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--iluria-color-background);
  z-index: 9999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 1.5rem;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 1.125rem;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 2rem;
}

.error-content {
  text-align: center;
  max-width: 32rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 1rem;
}

.error-message {
  font-size: 1rem;
  color: var(--iluria-color-text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.store-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.back-button {
  position: fixed;
  top: 1.5rem;
  left: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10000;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-1px);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .back-button {
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
  
  .loading-text {
    font-size: 1rem;
  }
  
  .error-title {
    font-size: 1.25rem;
  }
}
</style>