<template>
  <div class="page-editor-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="route.params.id ? t('pages.edit') : t('pages.add')"
      :subtitle="route.params.id ? 'Edite as informações da página' : 'Cadastre e edite informações de página'"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="true"
      :saveText="route.params.id ? t('update') : t('save')"
      @cancel-click="router.back()"
      @save-click="savePage"
    />

    <!-- Main Content -->
    <Form 
      v-slot="$form" 
      :resolver="resolver" 
      @submit.prevent="savePage" 
      :validate-on-blur="true" 
      :validate-on-value-update="false" 
      :initial-values="form"
    >
      <div class="editor-content">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Main Content Column -->
          <div class="lg:col-span-2">
            <ViewContainer
              :title="t('pages.pageData')"
              :icon="DocumentValidationIcon"
              iconColor="blue"
              class="page-data-custom-height"
            >
              <!-- Título -->
              <div class="form-group">
                <IluriaInputText 
                  id="title" 
                  name="title" 
                  :label="t('pages.title')" 
                  v-model="form.title" 
                  :formContext="$form.title" 
                  :required="true"
                />
              </div>

              <!-- Editor Mode Toggle -->
              <div class="form-group">
                <div class="editor-header-with-toggle">
                  <IluriaLabel>{{ t('pages.content') }}</IluriaLabel>
                  <div class="editor-mode-toggle">
                    <IluriaButton 
                      @click="enableVisualMode"
                      :hugeIcon="EyeIcon"
                      :color="!showHtmlMode ? 'primary' : 'ghost'"
                      :variant="!showHtmlMode ? 'solid' : 'ghost'"
                      size="small"
                      class="mode-toggle-button"
                      :class="{ 'active': !showHtmlMode }"
                    >
                      {{ t('pages.visualEditor') }}
                    </IluriaButton>
                    <IluriaButton 
                      @click="enableHtmlMode"
                      :hugeIcon="SourceCodeSquareIcon"
                      :color="showHtmlMode ? 'primary' : 'ghost'"
                      :variant="showHtmlMode ? 'solid' : 'ghost'"
                      size="small"
                      class="mode-toggle-button"
                      :class="{ 'active': showHtmlMode }"
                    >
                      {{ t('pages.htmlEditor') }}
                    </IluriaButton>
                  </div>
                </div>
              </div>

              <!-- Editor Content -->
              <div class="form-group">
                <template v-if="!showHtmlMode">
                  <IluriaEditor
                    id="content"
                    v-model="form.content"
                    :placeholder="t('pages.contentPlaceholder')"
                    height="400px"
                    :hideToggle="true"
                    :externalHtmlMode="showHtmlMode"
                  />
                </template>
                
                <div v-else class="html-editor-container">
                  <MonacoEditor
                    id="html-content"
                    v-model="form.content"
                    language="html"
                    height="400px"
                    :theme="monacoTheme"
                    :options="{
                      wordWrap: 'on',
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      scrollBeyondLastLine: false,
                      roundedSelection: false,
                      padding: { top: 12, bottom: 12, left: 12, right: 12 }
                    }"
                  />
                </div>

                <Message v-if="$form.content?.invalid" severity="error"> 
                  {{ $form.content.error?.message }} 
                </Message>
              </div>
            </ViewContainer>
          </div>

          <!-- Sidebar Column -->
          <div class="lg:col-span-1">
            <!-- Metadados e SEO -->
            <ViewContainer 
              :title="t('pages.metadata')"
              :icon="Globe02Icon"
              iconColor="yellow"
              class="mb-6"
            >
              <!-- Meta Título -->
              <div class="form-group">
                <IluriaInputText 
                  id="meta_title" 
                  name="meta_title" 
                  :label="t('pages.metaTitle')" 
                  v-model="form.metaTitle" 
                  :formContext="$form.metaTitle"
                  maxlength="70"
                  @input="limitMetaTitle"
                />
                <div class="character-count" :class="getMetaTitleClass()">
                  {{ form.metaTitle?.length || 0 }} {{ t('pages.metaTitleLength') }}
                </div>
              </div>
              
              <!-- Meta Descrição -->
              <div class="form-group">
                <IluriaLabel for="meta_description">{{ t('pages.metaDescription') }}</IluriaLabel>
                <Textarea
                  id="meta_description"
                  v-model="form.metaDescription"
                  rows="3"
                  class="meta-textarea"
                  :placeholder="t('pages.metaDescriptionPlaceholder')"
                  maxlength="160"
                  @input="limitMetaDescription"
                />
                <div class="character-count" :class="getMetaDescriptionClass()">
                  {{ form.metaDescription?.length || 0 }} {{ t('pages.metaDescriptionLength') }}
                </div>
              </div>
              
              <!-- Slug/URL -->
              <div class="form-group">
                <IluriaInputText 
                  id="slug" 
                  name="slug" 
                  :label="t('pages.slug')" 
                  v-model="form.slug" 
                  :formContext="$form.slug"
                  :placeholder="t('pages.slugPlaceholder')"
                  @input="formatSlug"
                />
                <small class="slug-help">{{ t('pages.slugHelp') }}</small>
              </div>
            </ViewContainer>
            
            <!-- Configurações -->
            <ViewContainer
              :title="t('pages.settings')"
              :icon="Settings02Icon"
              iconColor="purple"
            >
              <!-- Status de Publicação -->
              <div class="settings-item">
                <div class="settings-info">
                  <h4 class="settings-title">{{ t('pages.publishedProduction') }}</h4>
                </div>
                <div class="settings-toggle">
                  <IluriaToggleSwitch
                    id="activeProd"
                    name="activeProd"
                    v-model="form.activeProd"
                    :formContext="$form.activeProd"
                  />
                </div>
              </div>

              <div class="settings-item">
                <div class="settings-info">
                  <h4 class="settings-title">{{ t('pages.publishedDevelopment') }}</h4>
                </div>
                <div class="settings-toggle">
                  <IluriaToggleSwitch
                    id="activeDev"
                    name="activeDev"
                    v-model="form.activeDev"
                    :formContext="$form.activeDev"
                  />
                </div>
              </div>
            </ViewContainer>
          </div>
        </div>
      </div>
    </Form>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Form } from '@primevue/forms'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { z } from 'zod'
import PagesService from '@/services/pages.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import { IluriaInputText } from '@/components/iluria/form'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import Textarea from 'primevue/textarea'
import Message from 'primevue/message'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaEditor from '@/components/editor/IluriaEditor.vue'
import MonacoEditor from '@/components/editor/MonacoEditor.vue'
import {
  FloppyDiskIcon,
  DocumentValidationIcon,
  Globe02Icon,
  Settings02Icon,
  EyeIcon,
  SourceCodeSquareIcon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()

// Form default values
const form = ref({
  id: '',
  title: '',
  content: '',
  activeProd: false,
  activeDev: true,
  metaTitle: '',
  metaDescription: '',
  slug: '',
})

const showHtmlMode = ref(false)

// Detectar tema atual para o Monaco Editor
const currentTheme = ref('vs')

// Função para detectar o tema atual
const detectTheme = () => {
  const isDark = document.documentElement.classList.contains('theme-dark') || 
                 document.documentElement.getAttribute('data-theme') === 'dark' ||
                 getComputedStyle(document.documentElement).getPropertyValue('--iluria-theme-mode')?.trim() === 'dark'
  currentTheme.value = isDark ? 'vs-dark' : 'light'
}

// Computed para o tema do Monaco
const monacoTheme = computed(() => currentTheme.value)

// Observer para mudanças de tema
const themeObserver = new MutationObserver(() => {
  detectTheme()
})

// Funções para as classes dos character counts
const getMetaTitleClass = () => {
  const length = form.value.metaTitle?.length || 0
  if (length < 60) return 'count-normal'
  if (length >= 60 && length < 70) return 'count-warning'
  return 'count-error'
}

const getMetaDescriptionClass = () => {
  const length = form.value.metaDescription?.length || 0
  if (length < 136) return 'count-normal'
  if (length >= 136 && length < 160) return 'count-warning'
  return 'count-error'
}

// Funções para limitar o número de caracteres
function limitMetaTitle() {
  if (form.value.metaTitle && form.value.metaTitle.length > 70) {
    form.value.metaTitle = form.value.metaTitle.substring(0, 70)
  }
}

function limitMetaDescription() {
  if (form.value.metaDescription && form.value.metaDescription.length > 160) {
    form.value.metaDescription = form.value.metaDescription.substring(0, 160)
  }
}

function formatSlug() {
  if (form.value.slug) {
    form.value.slug = form.value.slug
      .toLowerCase()
      .replace(/\s+/g, '-')           
      .replace(/[^\w\-]+/g, '')        
      .replace(/\-\-+/g, '-')          
      .replace(/^-+/, '')              
      .replace(/-+$/, '')              
  }
}

const resolver = zodResolver(
  z.object({
    title: z.string().min(1, t('validation.required', { field: t('pages.title') })),
    content: z.string().min(1, t('validation.required', { field: t('pages.content') })),
    activeProd: z.boolean(),
    activeDev: z.boolean(),
    metaTitle: z.string().max(70).optional(),
    metaDescription: z.string().max(160).optional(),
    slug: z.string()
      .refine(value => !value || !/\s/.test(value), {
        message: t('validation.noSpaces', { field: t('pages.slug') })
      })
      .optional(),
  })
)

async function loadPage() {
  if (!route.params.id) return;
  try {
    const response = await PagesService.getPage(route.params.id)
    if (response) {
      form.value = response
    }
  } catch (error) {
    console.error('Error loading page:', error)
  }
}

function enableHtmlMode() {
  showHtmlMode.value = true;
}

function enableVisualMode() {
  showHtmlMode.value = false;
}

async function savePage() {
  try {
    formatSlug();
    
    if (route.params.id) {
      await PagesService.updatePage({
        ...form.value,
        id: route.params.id
      })
    } else {
      await PagesService.createPage(form.value)
    }
    router.push('/pages')
  } catch (error) {
    console.error('Error saving page:', error)
  }
}

onMounted(() => {
  loadPage()
  detectTheme()
  
  // Observar mudanças no tema
  themeObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class', 'data-theme']
  })
})

// Cleanup do observer
const cleanup = () => {
  themeObserver.disconnect()
}

// Cleanup quando o componente for desmontado
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanup)
}
</script>

<style scoped>
.page-editor-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}

/* Header */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  gap: 24px;
}

.header-content {
  flex: 1;
  min-width: 0;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  line-height: 1.4;
  word-wrap: break-word;
  hyphens: auto;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  flex-shrink: 0;
}

/* Main Content */
.editor-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Form */
.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* Editor Mode Toggle */
.editor-header-with-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.editor-mode-toggle {
  display: flex;
  gap: 0;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 4px;
  width: fit-content;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Customização dos IluriaButtons no toggle */
.mode-toggle-button {
  border-radius: 6px !important;
  border: none !important;
  font-weight: 500 !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  white-space: nowrap;
  flex-shrink: 0;
  font-size: 0.875rem !important;
  padding: 8px 12px !important;
  background: transparent !important;
}

.mode-toggle-button:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Estado ghost (inativo) */
.mode-toggle-button.btn-ghost {
  color: var(--iluria-color-text-secondary) !important;
  background: transparent !important;
}

.mode-toggle-button.btn-ghost:hover:not(:disabled) {
  background: var(--iluria-color-hover) !important;
  color: var(--iluria-color-text-primary) !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Estado primary (ativo) */
.mode-toggle-button.btn-primary {
  background: var(--iluria-color-primary) !important;
  color: var(--iluria-color-primary-contrast) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

.mode-toggle-button.btn-primary:hover:not(:disabled) {
  background: var(--iluria-color-primary-hover) !important;
  color: var(--iluria-color-primary-contrast) !important;
  transform: none !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.15), 0 1px 2px 0 rgba(0, 0, 0, 0.1) !important;
}

/* Força a cor dos ícones */
.mode-toggle-button svg {
  fill: currentColor !important;
  stroke: currentColor !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.mode-toggle-button svg path {
  fill: currentColor !important;
  stroke: currentColor !important;
}

.mode-toggle-button svg * {
  fill: inherit !important;
  stroke: inherit !important;
}

/* HTML Editor */
.html-editor-container {
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--iluria-color-surface);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Meta Fields */
.meta-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--iluria-color-input-border);
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--iluria-color-input-bg);
  color: var(--iluria-color-input-text);
}

.meta-textarea:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-focus-ring);
}

.meta-textarea::placeholder {
  color: var(--iluria-color-input-placeholder);
}

/* Character Count */
.character-count {
  font-size: 0.75rem;
  text-align: right;
  margin-top: 4px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.count-normal {
  color: var(--iluria-color-text-secondary);
}

.count-warning {
  color: var(--iluria-color-warning);
}

.count-error {
  color: var(--iluria-color-error);
}

.slug-help {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin-top: 4px;
  display: block;
}

/* Settings Items */
.settings-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 1rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.settings-item:last-child {
  margin-bottom: 0;
}

.settings-item:hover {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-info {
  flex: 1;
}

.settings-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.4;
}

.settings-toggle {
  flex-shrink: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .grid.grid-cols-1.lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-editor-container {
    padding: 16px;
  }

  .editor-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 16px;
  }

  .header-content h1.page-title {
    font-size: 24px;
  }

  .header-content .page-subtitle {
    font-size: 15px;
    margin: 6px 0 0 0;
  }

  .header-actions {
    justify-content: flex-end;
    align-self: flex-end;
  }

  .editor-header-with-toggle {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .editor-mode-toggle {
    width: 100%;
  }

  .mode-toggle-button {
    flex: 1 !important;
    justify-content: center !important;
  }

  .settings-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }

  .settings-toggle {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .page-editor-container {
    padding: 12px;
  }
  
  .header-content h1.page-title {
    font-size: 22px;
  }
  
  .header-content .page-subtitle {
    font-size: 14px;
    margin: 8px 0 0 0;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }
  
  .header-actions .btn {
    width: 100%;
  }
}

/* CSS PERSONALIZADO PARA PAGE DATA CONTAINER - 2px MAIOR */
.page-data-custom-height {
  min-height: calc(100% + 2px) !important;
  height: calc(100% + 2px) !important;
}

.page-data-custom-height .view-container {
  min-height: calc(100% + 2px) !important;
  height: calc(100% + 2px) !important;
}

.page-data-custom-height .container-content {
  min-height: calc(100% + 2px) !important;
  padding-bottom: calc(24px + 2px) !important; /* Padding inferior também 2px maior */
}
</style>
