{"teamManagement": {"title": "Gestão de Equipe", "subtitle": "Gerencie colaboradores e suas permissões na loja", "inviteCollaborator": "Convidar <PERSON>", "roles": "Cargos", "invites": "<PERSON><PERSON><PERSON>", "rolesDescription": "Configurar cargos e permissões", "invitesDescription": "Ver convites pendentes enviados para a equipe", "columns": {"user": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nome", "email": "Email", "role": "Cargo", "actions": "Ações"}, "actions": {"editRole": "Alterar cargo", "removeMember": "Remover da equipe"}, "memberInfo": {"since": "<PERSON><PERSON>"}, "emptyState": {"title": "Nenhum colaborador encontrado", "description": "Comece convidando colaboradores para sua equipe"}, "loadingState": {"message": "Carregando membros da equipe..."}, "confirmRemoval": {"title": "Confi<PERSON><PERSON>", "message": "Tem certeza que deseja remover {memberName} da equipe?"}, "messages": {"loadError": "Erro ao carregar membros da equipe", "removeSuccess": "Membro removido com sucesso", "removeError": "Erro ao remover membro"}}, "pendingInvites": {"title": "Convites Enviados", "subtitle": "Gerencie os convites pendentes da sua equipe", "columns": {"email": "Email", "role": "Cargo", "sentAt": "Enviado em", "actions": "Ações"}, "actions": {"cancel": "<PERSON><PERSON><PERSON>"}, "emptyState": {"title": "Nenhum convite pendente", "description": "Todos os convites foram aceitos ou expirados."}, "loadingState": {"message": "Carregando convites..."}, "confirmCancel": {"title": "<PERSON><PERSON><PERSON>", "message": "Tem certeza que deseja cancelar o convite para {email}?"}, "messages": {"loadError": "Erro ao carregar convites pendentes", "cancelSuccess": "Convite cancelado com sucesso", "cancelError": "Erro ao cancelar convite"}, "timeAgo": {"now": "<PERSON><PERSON>a mesmo", "minutesAgo": "{minutes}m atrás", "hoursAgo": "{hours}h atrás", "daysAgo": "{days}d atrás"}}, "roleManagement": {"title": "Configuração de Cargos", "subtitle": "Gerencie cargos e suas permissões na loja", "createRole": "<PERSON><PERSON><PERSON>", "editRole": "<PERSON><PERSON>", "back": "Voltar", "columns": {"name": "Nome do Cargo", "description": "Descrição", "permissions": "Permissões", "actions": "Ações"}, "systemBadge": "Sistema", "noDescription": "Sem descrição", "permissionsCount": "{count} per<PERSON><PERSON><PERSON><PERSON>", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "actions": {"edit": "Editar cargo", "delete": "Excluir cargo"}, "emptyState": {"title": "Nenhum cargo encontrado", "description": "Comece criando cargos personalizados para sua equipe"}, "loadingState": {"message": "Carregando cargos..."}}, "roleModal": {"createTitle": "<PERSON><PERSON><PERSON>", "editTitle": "<PERSON><PERSON>", "createSubtitle": "Configure um novo cargo para sua equipe", "editSubtitle": "Modifique as informações do cargo", "save": "<PERSON><PERSON>", "form": {"name": "Nome do Cargo", "nameRequired": "Nome do cargo é obrigatório", "namePlaceholder": "Ex: Designer, <PERSON><PERSON>", "description": "Descrição", "descriptionPlaceholder": "Descrição do cargo (opcional)"}, "permissions": {"title": "Permissões", "description": "Selecione as permissões que este cargo terá na loja", "selectAll": "Selecionar todos", "unselectAll": "<PERSON><PERSON><PERSON> to<PERSON>", "categories": {"store": "Gerenciamento da Loja", "product": "<PERSON><PERSON><PERSON>", "order": "Pedidos", "customer": "Clientes", "financial": "Financeiro", "promotion": "Promoções", "layout": "Layout e Design", "file": "<PERSON>r<PERSON><PERSON>", "analytics": "Analytics", "team": "Equipe", "payment": "Pagamento", "shipping": "Frete", "notification": "Notificações da Loja"}}, "validation": {"nameRequired": "Nome do cargo é obrigatório", "permissionsRequired": "Selecione pelo menos uma permissão"}, "messages": {"createSuccess": "Cargo criado com sucesso", "updateSuccess": "Cargo atualizado com sucesso", "createError": "Erro ao criar cargo", "updateError": "Erro ao salvar cargo", "loadPermissionsError": "Erro ao carregar permissões disponíveis"}}, "viewPermissions": {"title": "Permissões: {roleName}", "subtitle": "Visualizar todas as permissões deste cargo", "noPermissions": "Este cargo não possui permissões configuradas."}, "confirmDelete": {"title": "Excluir Cargo", "message": "Tem certeza que deseja excluir o cargo \"{roleName}\"?", "success": "Cargo excluído com sucesso", "error": "Erro ao excluir cargo"}, "permissions": {"store": {"admin": "Administração completa da loja", "settings": "Gerenciar configurações da loja", "view": "Visualizar informações da loja"}, "product": {"create": "<PERSON><PERSON><PERSON> produtos", "edit": "<PERSON><PERSON> produtos", "delete": "Excluir produtos", "view": "Visualizar produtos", "category": {"manage": "Gerenciar categorias"}}, "order": {"view": "Visualizar pedidos", "edit": "<PERSON><PERSON> ped<PERSON>", "cancel": "<PERSON><PERSON><PERSON> pedidos", "export": "Exportar pedidos", "status": {"change": "Alterar status de pedidos"}}, "customer": {"view": "Visualizar clientes", "edit": "Editar clientes", "delete": "Excluir clientes", "export": "Exportar dados de clientes"}, "financial": {"view": "Visualizar dados financeiros", "reports": "Acessar relatórios financeiros", "export": "Exportar dados financeiros"}, "promotion": {"create": "Criar promo<PERSON>", "edit": "Editar promoções", "delete": "Excluir promoções", "view": "Visualizar promoções"}, "layout": {"edit": "Editar <PERSON> da loja", "publish": "Publicar alterações de layout", "template": {"manage": "Gerenciar templates"}}, "file": {"upload": "Upload de arquivos", "delete": "Excluir arquivos", "manage": "Gerenciar arquivos da loja"}, "analytics": {"view": "Visualizar analytics"}, "reports": {"generate": "<PERSON><PERSON><PERSON>", "export": "Exportar relatórios"}, "team": {"manage": "Gerenciar equipe", "invite": "<PERSON><PERSON><PERSON> membros", "remove": "Remover membros", "roles": {"manage": "Gerenciar cargos"}}, "payment": {"settings": "Configurar pagamentos", "view": "Visualizar configurações de pagamento"}, "shipping": {"settings": "Configurar frete", "view": "Visualizar configurações de frete"}, "notification": {"manage": "Gerenciar notificações da loja", "send": "Enviar notificações"}}, "inviteMember": {"title": "Convidar <PERSON>", "subtitle": "Adicione um novo membro à sua equipe", "saveLabel": "<PERSON><PERSON><PERSON>", "form": {"email": {"label": "Email do colaborador", "placeholder": "Digite o email do colaborador", "required": "Email é obrigatório", "invalid": "<PERSON><PERSON>"}, "role": {"label": "Cargo", "placeholder": "Selecione o cargo", "loadingPlaceholder": "Carregando cargos...", "required": "Cargo é obrigatório"}, "message": {"label": "Mensagem personalizada (opcional)", "placeholder": "Adicione uma mensagem personalizada ao convite...", "characterCount": "{current}/{max}"}}, "validation": {"checking": "Verificando email...", "found": "Email encontrado no sistema", "notFound": "Email não encontrado. O usuário precisa ter uma conta no sistema."}, "messages": {"loadRolesError": "Erro ao carregar cargos disponíveis", "sendSuccess": "Convite enviado com sucesso!", "sendError": "Erro ao enviar convite. Tente novamente."}}}