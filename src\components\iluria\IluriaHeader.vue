<template>
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">{{ title }}</h1>
            <p class="page-subtitle" v-if="subtitle">{{ subtitle }}</p>
        </div>
        <div class="header-actions">
            <div class="search-container" v-if="showSearch">
                <IluriaInputText type="text" v-model="searchValue" :placeholder="searchPlaceholder" @input="onSearch"
                    class="search-input" />
            </div>

            <!-- Custom Buttons (até 4 botões personalizados) -->
            <template v-for="(button, index) in validCustomButtons" :key="`custom-${index}`">
                <IluriaButton
                    :color="button.color || 'secondary'"
                    :variant="button.variant || 'solid'"
                    :hugeIcon="button.icon"
                    :disabled="button.disabled || false"
                    :loading="button.loading || false"
                    :size="button.size || 'medium'"
                    @click="handleCustomClick(index, button)"
                    :class="['custom-header-button', button.class]"
                    :title="button.tooltip"
                >
                    {{ button.text }}
                </IluriaButton>
            </template>

            <!-- Slot para botões completamente customizados -->
            <slot name="customButtons"></slot>

            <IluriaButton v-if="showCancel" @click="$emit('cancel-click')" size="medium" variant="outline">
                {{ cancelText }}
            </IluriaButton>
            <IluriaButton v-if="showAdd" @click="$emit('add-click')" :hugeIcon="addIcon" size="medium">
                {{ addText }}
            </IluriaButton>
            <IluriaButton v-if="showSave" @click.prevent="$emit('save-click')" :hugeIcon="saveIcon" size="medium">
                {{ saveText }}
            </IluriaButton>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { debounce } from 'lodash-es'
import IluriaInputText from '../../components/iluria/form/IluriaInputText.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { PlusSignSquareIcon, FloppyDiskIcon } from '@hugeicons-pro/core-stroke-rounded'

const props = defineProps({
    title: String,
    subtitle: String,
    showSearch: {
        type: Boolean,
        default: false
    },
    showAdd: {
        type: Boolean,
        default: false
    },
    addText: {
        type: String,
        default: ''
    },
    addIcon: {
        type: Object,
        default: PlusSignSquareIcon
    },
    showCancel: {
        type: Boolean,
        default: false
    },
    cancelText: {
        type: String,
        default: ''
    },
    showSave: {
        type: Boolean,
        default: false
    },
    saveText: {
        type: String,
        default: ''
    },
    saveIcon: {
        type: Object,
        default: FloppyDiskIcon
    },
    searchPlaceholder: {
        type: String,
        default: 'Buscar...'
    },
    debounceDelay: {
        type: Number,
        default: 300
    },
    // Botões customizáveis (até 4)
    customButtons: {
        type: Array,
        default: () => [],
        validator: (buttons) => {
            // Máximo de 4 botões
            if (buttons.length > 4) {
                console.warn('IluriaHeader: Máximo de 4 botões customizáveis permitidos')
                return false
            }

            // Cada botão deve ter pelo menos text
            return buttons.every(button =>
                button && typeof button === 'object' && button.text
            )
        }
    }
})

const emit = defineEmits(['search', 'button-click', 'save-click', 'cancel-click', 'add-click', 'custom-click'])

const searchValue = ref('')

// Computed para validar e filtrar botões customizáveis
const validCustomButtons = computed(() => {
    return props.customButtons
        .filter(button => button && button.text) // Remove botões inválidos
        .slice(0, 4) // Máximo de 4 botões
})

// Função para lidar com cliques em botões customizáveis
const handleCustomClick = (index, button) => {
    // Se o botão tem uma função onClick personalizada, executa ela
    if (button.onClick && typeof button.onClick === 'function') {
        button.onClick(button, index)
    }

    // Sempre emite o evento para o componente pai
    emit('custom-click', index, button)
}

const onSearch = debounce(() => {
    emit('search', searchValue.value)
}, props.debounceDelay)

watch(() => props.showSearch, (val) => {
    if (!val) searchValue.value = ''
})
</script>

<style scoped>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--iluria-color-border);
    transition: border-color 0.3s ease;
}

.header-content {
    flex: 1;
    min-width: 0; /* Permite que o conteúdo seja comprimido se necessário */
}

.header-content h1.page-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--iluria-color-text-primary);
    margin: 0;
    line-height: 1.2;
    transition: color 0.3s ease;
    word-wrap: break-word; /* Quebra palavras longas se necessário */
}

.header-content .page-subtitle {
    font-size: 15px;
    color: var(--iluria-color-text-secondary);
    margin: 4px 0 0 0; /* Reduzido o espaçamento superior */
    line-height: 1.4;
    transition: color 0.3s ease;
    word-wrap: break-word; /* Quebra palavras longas se necessário */
    max-width: 100%; /* Garante que não ultrapasse o container */
}

.header-actions {
    display: flex;
    align-items: center; /* Centraliza os botões verticalmente */
    gap: 16px;
    flex-shrink: 0; /* Impede que os botões sejam comprimidos */
}

.search-container {
    position: relative;
}

.search-input {
    min-width: 300px;
}

/* Estilos para botões customizáveis */
.custom-header-button {
    margin-left: 8px;
    transition: all 0.2s ease;
}

.custom-header-button:first-of-type {
    margin-left: 12px;
}

.custom-header-button:hover {
    transform: translateY(-1px);
}

/* Responsividade para botões customizáveis */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }

    .header-content {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .header-actions {
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 0;
        align-items: center; /* Em mobile, centraliza os botões */
    }

    .custom-header-button {
        margin-left: 0;
        flex: 1;
        min-width: 120px;
    }

    .search-container {
        width: 100%;
        margin-bottom: 8px;
    }

    .search-input {
        min-width: 100%;
    }
}
</style>