{"teamManagement": {"title": "Team Management", "subtitle": "Manage collaborators and their permissions in the store", "inviteCollaborator": "Invite Collaborator", "roles": "Roles", "invites": "<PERSON><PERSON><PERSON>", "rolesDescription": "Configure roles and permissions", "invitesDescription": "View pending invites sent to the team", "columns": {"user": "User", "name": "Name", "email": "Email", "role": "Role", "actions": "Actions"}, "actions": {"editRole": "Change role", "removeMember": "<PERSON><PERSON><PERSON> from team"}, "memberInfo": {"since": "Since"}, "emptyState": {"title": "No collaborators found", "description": "Start by inviting collaborators to your team"}, "loadingState": {"message": "Loading team members..."}, "confirmRemoval": {"title": "Confirm Removal", "message": "Are you sure you want to remove {member<PERSON><PERSON>} from the team?"}, "messages": {"loadError": "Error loading team members", "removeSuccess": "Member removed successfully", "removeError": "Error removing member"}}, "pendingInvites": {"title": "<PERSON><PERSON>", "subtitle": "Manage your team's pending invites", "columns": {"email": "Email", "role": "Role", "sentAt": "Sent at", "actions": "Actions"}, "actions": {"cancel": "Cancel"}, "emptyState": {"title": "No pending invites", "description": "All invites have been accepted or expired."}, "loadingState": {"message": "Loading invites..."}, "confirmCancel": {"title": "Cancel Invite", "message": "Are you sure you want to cancel the invite for {email}?"}, "messages": {"loadError": "Error loading pending invites", "cancelSuccess": "Invite cancelled successfully", "cancelError": "Error cancelling invite"}, "timeAgo": {"now": "Just now", "minutesAgo": "{minutes}m ago", "hoursAgo": "{hours}h ago", "daysAgo": "{days}d ago"}}, "roleManagement": {"title": "Role Configuration", "subtitle": "Manage roles and their permissions in the store", "createRole": "Create Role", "editRole": "Edit Role", "back": "Back", "columns": {"name": "Role Name", "description": "Description", "permissions": "Permissions", "actions": "Actions"}, "systemBadge": "System", "noDescription": "No description", "permissionsCount": "{count} permissions", "viewDetails": "View details", "actions": {"edit": "Edit role", "delete": "Delete role"}, "emptyState": {"title": "No roles found", "description": "Start by creating custom roles for your team"}, "loadingState": {"message": "Loading roles..."}}, "roleModal": {"createTitle": "Create Role", "editTitle": "Edit Role", "createSubtitle": "Configure a new role for your team", "editSubtitle": "Modify role information", "save": "Save", "form": {"name": "Role Name", "nameRequired": "Role name is required", "namePlaceholder": "Ex: Designer, Marketer", "description": "Description", "descriptionPlaceholder": "Role description (optional)"}, "permissions": {"title": "Permissions", "description": "Select the permissions this role will have in the store", "selectAll": "Select all", "unselectAll": "Unselect all", "categories": {"store": "Store Management", "product": "Products", "order": "Orders", "customer": "Customers", "financial": "Financial", "promotion": "Promotions", "layout": "Layout and Design", "file": "Files", "analytics": "Analytics", "team": "Team", "payment": "Payment", "shipping": "Shipping", "notification": "Store Notifications"}}, "validation": {"nameRequired": "Role name is required", "permissionsRequired": "Select at least one permission"}, "messages": {"createSuccess": "Role created successfully", "updateSuccess": "Role updated successfully", "createError": "Error creating role", "updateError": "Error saving role", "loadPermissionsError": "Error loading available permissions"}}, "viewPermissions": {"title": "Permissions: {roleName}", "subtitle": "View all permissions for this role", "noPermissions": "This role has no configured permissions."}, "confirmDelete": {"title": "Delete Role", "message": "Are you sure you want to delete the role \"{roleName}\"?", "success": "Role deleted successfully", "error": "Error deleting role"}, "permissions": {"store": {"admin": "Complete store administration", "settings": "Manage store settings", "view": "View store information"}, "product": {"create": "Create products", "edit": "Edit products", "delete": "Delete products", "view": "View products", "category": {"manage": "Manage categories"}}, "order": {"view": "View orders", "edit": "Edit orders", "cancel": "Cancel orders", "export": "Export orders", "status": {"change": "Change order status"}}, "customer": {"view": "View customers", "edit": "Edit customers", "delete": "Delete customers", "export": "Export customer data"}, "financial": {"view": "View financial data", "reports": "Access financial reports", "export": "Export financial data"}, "promotion": {"create": "Create promotions", "edit": "Edit promotions", "delete": "Delete promotions", "view": "View promotions"}, "layout": {"edit": "Edit store layout", "publish": "Publish layout changes", "template": {"manage": "Manage templates"}}, "file": {"upload": "Upload files", "delete": "Delete files", "manage": "Manage store files"}, "analytics": {"view": "View analytics"}, "reports": {"generate": "Generate reports", "export": "Export reports"}, "team": {"manage": "Manage team", "invite": "Invite members", "remove": "Remove members", "roles": {"manage": "Manage roles"}}, "payment": {"settings": "Configure payments", "view": "View payment settings"}, "shipping": {"settings": "Configure shipping", "view": "View shipping settings"}, "notification": {"manage": "Manage store notifications", "send": "Send notifications"}}, "inviteMember": {"title": "Invite Collaborator", "subtitle": "Add a new member to your team", "saveLabel": "Send Invite", "form": {"email": {"label": "Collaborator email", "placeholder": "Enter collaborator's email", "required": "Email is required", "invalid": "Invalid email"}, "role": {"label": "Role", "placeholder": "Select role", "loadingPlaceholder": "Loading roles...", "required": "Role is required"}, "message": {"label": "Custom message (optional)", "placeholder": "Add a custom message to the invite...", "characterCount": "{current}/{max}"}}, "validation": {"checking": "Checking email...", "found": "Email found in system", "notFound": "Email not found. User needs to have an account in the system."}, "messages": {"loadRolesError": "Error loading available roles", "sendSuccess": "<PERSON><PERSON><PERSON> sent successfully!", "sendError": "Error sending invite. Please try again."}}}