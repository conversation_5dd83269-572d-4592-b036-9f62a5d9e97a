{"title": "<PERSON><PERSON>", "description": "Gere<PERSON><PERSON> e personalize os temas da sua loja", "addTheme": "<PERSON><PERSON><PERSON>", "initializeDefaults": "<PERSON><PERSON><PERSON><PERSON>", "initializeDefaultsTooltip": "Adiciona temas padrão do sistema à sua loja", "themes": "<PERSON><PERSON>", "themeName": "Nome do Tema", "themeDescription": "Descrição do Tema", "published": "Publicado", "notPublished": "Não publicado", "themeLibrary": "Biblioteca de Temas", "noFiltersAvailable": "Nenhum filtro disponível", "filterBy": "Filtrar por {category}", "viewOptions": "Opções de visualização", "gridView": "Visualização em grade", "listView": "Visualização em lista", "tags": {"moderno": "Moderno", "fashion": "Fashion", "minimalista": "Minimalista", "luxo": "Luxo", "tecnologia": "Tecnologia", "escandinavo": "Escandinavo", "dark": "Dark", "vibrante": "<PERSON>ib<PERSON><PERSON>", "clean": "Clean"}, "categoriesTitle": "Categorias", "hintDomainUrl": "O dominío é o endereço do seu site, que seus clientes pesquisarão para acessá-lo.", "categories": {"moderno": "Moderno", "fashion": "Fashion", "minimalista": "Minimalista", "luxo": "Luxo", "tecnologia": "Tecnologia", "escandinavo": "Escandinavo", "dark": "Dark", "vibrante": "<PERSON>ib<PERSON><PERSON>"}, "preview": {"title": "Visualização do Tema", "desktop": "Desktop", "tablet": "Tablet", "mobile": "Mobile", "selectTheme": "Selecionar Tema", "dualView": "Visualização Dupla", "singleView": "Visualização Única"}, "hidePreview": "Ocultar Preview", "showPreview": "Mostrar Preview", "previewSimple": "Visualizar", "fullPreview": "Visualização Completa", "current": "Atual", "select": "Selecionar", "useTheme": "<PERSON><PERSON>", "customize": "Personalizar", "previewStore": "<PERSON><PERSON><PERSON>", "backToThemes": "Voltar aos Temas", "retry": "Tentar Novamente", "previewError": "Erro na Visualização", "previewErrorMessage": "Não foi possível carregar a visualização da loja. Tente novamente.", "previewTimeoutError": "A visualização da loja demorou muito para carregar. Verifique sua conexão e tente novamente.", "noStoreSelected": "Nenhuma loja selecionada. Selecione uma loja primeiro.", "backendNotAvailable": "Serviço de visualização da loja não está disponível. Certifique-se de que o servidor backend está rodando na porta 8081.", "publish": {"publish": "Publicar", "published": "Publicado", "configureDomain": "<PERSON><PERSON><PERSON><PERSON>", "waitingSSL": "Aguardando SSL", "publishSuccess": "Site publicado com sucesso!", "publishError": "Erro ao publicar o site", "confirmPublish": {"title": "Publicar Site", "message": "Tem certeza que deseja publicar o site? Isso atualizará sua loja online com as alterações mais recentes."}, "status": {"upToDate": "Sua loja está atualizada e publicada", "outdated": "Pronto para publicar as alterações", "noDomain": "Configure um domínio válido em Configurações > <PERSON><PERSON><PERSON>", "noSSL": "Aguardando instalação do certificado SSL"}}, "confirmUseTheme": {"title": "Aplicar <PERSON>", "message": "Tem certeza que deseja aplicar o tema \"{themeName}\"? Isso substituirá o tema atual da sua loja."}, "themeAppliedSuccess": "<PERSON><PERSON> \"{themeName}\" aplicado com sucesso!", "errorApplyingTheme": "Erro ao aplicar o tema. Tente novamente.", "success": "Sucesso", "error": "Erro", "activate": "Ativar", "rename": "Renomear", "changeColors": "<PERSON><PERSON>", "duplicate": "Duplicar", "download": "Baixar", "editCode": "<PERSON><PERSON>", "editContent": "<PERSON><PERSON>", "delete": "Excluir", "currentTheme": "<PERSON><PERSON>", "noThemesAvailable": "Nenhum tema disponível", "noThemesDescription": "Os temas padrão do Iluria estão sendo carregados. Se o problema persistir, verifique sua conexão.", "colorPalette": "Paleta de Cores", "details": "<PERSON><PERSON><PERSON>", "layout": "Layout", "author": "Autor", "version": "Vers<PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "back": "Voltar", "forward": "<PERSON><PERSON><PERSON><PERSON>", "home": "Início", "previewUrl": "URL de Preview", "loadingPreview": "Carregando preview...", "layoutContainer": "Container", "layoutFullWidth": "Largura Total", "layoutContainerDesc": "Conteúdo centralizado com margens laterais", "layoutFullWidthDesc": "<PERSON>te<PERSON><PERSON> ocupa toda a largura da tela", "basedOn": "Baseado em", "summary": "Resumo", "setAsCurrent": "Definir como tema atual", "setAsCurrentDesc": "Aplicar este tema imediatamente após a criação", "colorPreviewText": "Este é um exemplo de como o tema ficará com as cores selecionadas.", "resetColors": "Redefinir Cores", "randomColors": "<PERSON><PERSON>", "primaryColor": "<PERSON><PERSON>", "secondaryColor": "<PERSON><PERSON>", "backgroundColor": "<PERSON><PERSON> <PERSON>", "textColor": "<PERSON><PERSON> <PERSON>", "renameDescription": "Digite um novo nome para o tema", "currentName": "Nome atual", "newName": "Novo nome", "enterNewName": "Digite o novo nome do tema", "renaming": "Renomeando...", "renameInfo": "O nome do tema será alterado apenas para você. Outros usuários não serão afetados.", "nameRequired": "Nome é obrigatório", "nameTooShort": "Nome deve ter pelo menos 2 caracteres", "nameTooLong": "Nome deve ter no máximo 100 caracteres", "nameSameAsCurrent": "O novo nome deve ser diferente do atual", "nameAlreadyExists": "Já existe um tema com este nome", "renameError": "Erro ao renomear tema. Tente novamente.", "changeColorsDescription": "Personalize as cores do seu tema", "saveColors": "<PERSON><PERSON>", "savingColors": "Salvando...", "primaryButton": "Botão Primário", "secondaryButton": "Botão Secundário", "sampleText": "Texto de exemplo para visualização", "changeColorsInfo": "As cores serão aplicadas apenas a este tema. Você pode reverter as alterações a qualquer momento.", "colorWheel": "Roda de Cores", "colorConfiguration": "Configuração de Cores", "backgroundGeneral": "Fundo Geral", "backgroundComponents": "Fundo dos Componentes", "titleColor": "<PERSON><PERSON> Títulos", "harmoniousColors": "<PERSON><PERSON>", "applyColor": "Aplicar Cor", "stylePreviewText": "Veja como ficará o seu tema com as cores selecionadas", "aiSuggestions": "Sugestões de IA", "comingSoon": "Em Breve", "tryIt": "Experimentar", "create": {"title": "<PERSON><PERSON><PERSON> <PERSON>", "step1": "Informações Básicas", "step2": "Selecionar Tema Base", "step3": "Personalizar <PERSON>s", "step4": "Configurações Finais", "previous": "Anterior", "next": "Próximo", "finish": "Finalizar", "category": "Criar Nova Categoria"}, "categoryDescriptions": {"moderno": "Temas com design contemporâneo e clean", "fashion": "Temas para lojas de moda e vestuário", "minimalista": "Temas com design simples e elegante", "luxo": "Temas sofisticados para produtos premium", "tecnologia": "Temas para produtos tech e eletrônicos", "escandinavo": "Temas com estilo nórdico e natural", "dark": "Temas com modo escuro", "vibrante": "Temas com cores vivas e energéticas"}, "template": {"apply": "Aplicar Template", "version": "Versão do Template", "upload": "Enviar Template", "download": "Baixar Template", "status": "Status do Template", "available": "Disponível", "notAvailable": "Não Disponível", "updating": "Atualizando...", "pending": "Pendente", "error": "<PERSON><PERSON> no Template", "synced": "Template Sincronizado", "files": "{count} arquivo(s)"}, "onboarding": {"title": "Bem-vindo à Personalização da sua Loja!", "description": "Escolha um tema para começar a personalizar a aparência da sua loja online. O tema define as cores, tipografia e layout geral.", "step1": "Escolha um tema da biblioteca abaixo", "step2": "Personalize cores e estilos conforme sua marca", "step3": "Publique e comece a vender!", "chooseTheme": "<PERSON><PERSON><PERSON><PERSON>"}, "ai": {"improveColors": "<PERSON><PERSON><PERSON>", "optimizeLayout": "<PERSON><PERSON><PERSON><PERSON>", "suggestions": {"improveColors": {"title": "<PERSON><PERSON><PERSON>", "description": "Sua paleta atual pode ter melhor contraste para aumentar conversões em 12%"}, "optimizeLayout": {"title": "Otimizar Layout Mobile", "description": "Reorganizar elementos pode melhorar a experiência mobile e reduzir bounce rate"}, "improvePerformance": {"title": "<PERSON><PERSON><PERSON>", "description": "Otimizações automáticas podem reduzir tempo de carregamento em 1.2s"}, "generateVariants": {"title": "Gerar Variações A/B", "description": "Criamos 3 variações do seu tema para testes A/B automáticos"}, "checkAccessibility": {"title": "Verificar Acessibilidade", "description": "Detectamos 3 problemas de acessibilidade que podem ser corrigidos automaticamente"}}}, "templates": {"title": "Templates", "upload": "Enviar Templates", "sync": "Sincronizar Templates", "download": "Baixar Templates", "remove": "Remover Templates", "processForEditor": "Processar para Editor", "uploadSuccess": "Templates enviados com sucesso", "syncSuccess": "Templates sincronizados com sucesso", "downloadSuccess": "Templates baixados com sucesso", "removeSuccess": "Templates removidos com sucesso", "processingSuccess": "Templates processados com sucesso ({count} arquivos)", "processingPartialSuccess": "Processamento parcial: {success} sucessos, {errors} erros", "singleProcessingSuccess": "Template {fileName} processado com sucesso", "singleProcessingError": "Erro ao processar {fileName}: {error}", "uploadError": "Erro ao enviar templates", "syncError": "Erro ao sincronizar templates", "downloadError": "Erro ao baixar templates", "removeError": "Erro ao remover templates", "processingDescription": "Processa templates para compatibilidade com o editor de layout", "features": {"editorAttributes": "Adiciona atributos data-component e data-element-type", "productApi": "Integra API de produtos para funcionamento no editor", "spacingElements": "Identifica e marca elementos de espaçamento", "textElements": "Marca elementos de texto para edição", "imageElements": "Marca imagens para edição"}, "warnings": {"backupRecommended": "Recomendado fazer backup antes do processamento", "testAfterProcessing": "Teste o tema após o processamento"}}, "errors": {"invalidThemeId": "ID do tema inválido", "invalidParameters": "Parâmet<PERSON>", "processingFailed": "Falha no processamento dos templates"}, "confirmActivation": {"title": "Ativar <PERSON>", "message": "Deseja ativar o tema \"{themeName}\"? O tema atual será substituído.", "confirm": "Ativar <PERSON>"}, "confirmDuplication": {"title": "<PERSON>p<PERSON><PERSON>", "message": "<PERSON>eja criar uma cópia do tema \"{themeName}\"?", "confirm": "<PERSON>p<PERSON><PERSON>"}, "duplicating": {"title": "Duplicando tema", "message": "O tema \"{themeName}\" está sendo duplicado. Aguarde alguns instantes..."}, "confirmDeletion": {"title": "Excluir <PERSON>", "message": "Esta ação não pode ser desfeita. Deseja excluir o tema \"{themeName}\"?", "confirm": "Excluir <PERSON>"}, "backup": {"title": "<PERSON>t<PERSON><PERSON><PERSON>", "subtitle": "Gerencie backups automáticos e restaure versões anteriores dos seus temas", "create": "<PERSON><PERSON><PERSON>", "history": "Hist<PERSON><PERSON><PERSON>", "historyOf": "<PERSON><PERSON><PERSON><PERSON><PERSON> Backups - {themeName}", "loading": "Carregando backups...", "empty": {"title": "Nenhum backup encontrado", "description": "Os backups s<PERSON> criados automaticamente quando você troca de tema. Você também pode criar backups manuais."}, "actions": {"restore": "Restaurar", "delete": "Remover"}, "delete": {"title": "Remover Backup", "message": "Tem certeza que deseja remover este backup do tema", "warning": "Esta ação não pode ser desfeita."}, "restore": {"title": "<PERSON><PERSON><PERSON>", "message": "Tem certeza que deseja restaurar este backup do tema", "warning": "<PERSON><PERSON> substituirá os templates atuais do tema pelos salvos neste backup."}, "stats": {"totalBackups": "Total de Backups", "totalSize": "Espaço Ocupado"}, "alerts": {"nearExpiration": "Backups próximos do vencimento", "nearExpirationMessage": "{count} backup(s) irão expirar em breve"}, "table": {"noBackupsForTheme": "Nenhum backup encontrado para este template.", "backupCount": "{count} backup(s)", "themeNameFallback": "Template sem nome", "headers": {"type": "Tipo", "date": "Data", "size": "<PERSON><PERSON><PERSON>", "files": "<PERSON>r<PERSON><PERSON>", "status": "Status", "restored": "Restaurado", "actions": "Ações"}}, "modal": {"backupDate": "Data do Backup:", "files": "Arquivos:", "size": "Tamanho:", "fileCount": "{count} arquivo(s)"}}, "addCategory": "Adicionar Categoria", "categoryName": "Nome da Categoria", "categoryDescription": "Descrição da Categoria", "categoryColor": "Cor da categoria"}