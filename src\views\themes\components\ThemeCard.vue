<template>
  <div 
    class="theme-card"
    :class="{
      'theme-card--current': isCurrent,
      'theme-card--premium': theme.isPremium,
      'theme-card--list': viewMode === 'list'
    }"
  >
    <!-- Preview Section -->
    <div class="theme-preview">
      <div class="preview-container">
        <iframe
          v-if="showPreview"
          :src="previewUrl"
          class="preview-iframe"
          title="Theme preview"
          @load="onPreviewLoad"
          @error="onPreviewError"
          sandbox="allow-scripts allow-same-origin"
        ></iframe>
        
        <div
          v-else
          class="preview-placeholder"
          :style="{ backgroundColor: theme.cssVariables?.['color-background-general'] || '#f8f9fa' }"
        >
          <div class="preview-placeholder-content">
            <div class="preview-icon-wrapper">
              <HugeiconsIcon :icon="PaintBoardIcon" class="preview-icon" />
            </div>
            <span>{{ $t('themes.preview.title') }}</span>
          </div>
        </div>
        
        <!-- Loading overlay -->
        <div v-if="isLoading" class="preview-loading">
          <div class="loading-spinner"></div>
        </div>
        
        <!-- Theme change progress overlay -->
        <div v-if="isThisThemeChanging" class="theme-progress-overlay">
          <div class="progress-content">
            <div class="progress-spinner"></div>
            <div class="progress-info">
              <p class="progress-message">{{ themeProgress.message }}</p>
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: `${themeProgress.progress}%` }"
                ></div>
              </div>
              <span class="progress-percentage">{{ themeProgress.progress }}%</span>
              <!-- WebSocket connection status -->
              <div class="websocket-status" :class="{ 'websocket-connected': isConnected }">
                <div class="websocket-indicator"></div>
                <span class="websocket-text">
                  {{ isConnected ? 'Tempo real ativo' : 'Modo offline' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Preview controls -->
      <div class="preview-controls">
        <IluriaButton
          variant="ghost"
          size="small"
          @click="openFullPreview"
          :hugeIcon="MaximizeIcon"
        >
          {{ $t('themes.fullPreview') }}
        </IluriaButton>
      </div>
      
      <!-- Premium badge -->
      <div v-if="theme.isPremium" class="premium-badge">
        <HugeiconsIcon :icon="CrownIcon" />
        Premium
      </div>
      
      <!-- Current theme badge -->
      <div v-if="isCurrent" class="current-badge">
        <HugeiconsIcon :icon="CheckmarkSquare02Icon" />
        {{ $t('themes.current') }}
      </div>
      
      <!-- Template status badge -->
      <div v-if="templateStatus" class="template-badge" :class="templateStatus.class">
        <HugeiconsIcon :icon="templateStatus.icon" />
        {{ templateStatus.text }}
      </div>
    </div>
    
    <!-- Theme Info -->
    <div class="theme-info">
      <div class="theme-header">
        <InlineEditableText
          ref="themeNameEditableRef"
          v-model="theme.name"
          tag="h3"
          text-class="theme-name"
          :max-length="100"
          :min-length="2"
          @save="handleThemeNameSave"
          @validation-error="handleValidationError"
        />
        <p class="theme-description">{{ theme.description }}</p>
      </div>
      
      <!-- Tags -->
      <div class="theme-tags" v-if="theme.tags && theme.tags.length">
        <span 
          v-for="tag in theme.tags.slice(0, 3)"
          :key="tag"
          class="tag"
        >
          {{ $t(`themes.tags.${tag}`) || tag }}
        </span>
        <span v-if="theme.tags.length > 3" class="tag tag--more">
          +{{ theme.tags.length - 3 }}
        </span>
      </div>



      <!-- Actions -->
      <div class="theme-actions">
        <div class="primary-actions">
          <!-- Botão para temas padrão -->
          <IluriaButton
            v-if="theme.isDefault"
            variant="solid"
            color="primary"
            @click="useDefaultTheme"
            :loading="isSelecting || isThemeChanging"
            :disabled="isThemeChanging && !isThisThemeChanging"
          >
            {{ isThisThemeChanging ? progressMessage : $t('themes.useTheme') }}
          </IluriaButton>

          <!-- Botões para temas da loja -->
          <template v-else>
            <IluriaButton
              v-if="!isCurrent"
              variant="solid"
              color="primary"
              @click="selectTheme"
              :loading="isSelecting || isThemeChanging"
              :disabled="isThemeChanging && !isThisThemeChanging"
            >
              {{ isThisThemeChanging ? progressMessage : $t('themes.select') }}
            </IluriaButton>

            <IluriaButton
              v-else
              variant="solid"
              color="primary"
              @click="customizeTheme"
              :disabled="isThemeChanging"
            >
              {{ $t('themes.customize') }}
            </IluriaButton>
          </template>
          
          <IluriaButton
            variant="outline"
            @click="previewTheme"
          >
            {{ $t('themes.preview.title') }}
          </IluriaButton>
          
          <!-- Template actions -->
          <div v-if="templateStatus && templateStatus.class !== 'template-badge--error'" class="template-actions">
            <IluriaButton
              v-if="templateStatus.class === 'template-badge--pending'"
              variant="outline"
              size="small"
              @click="syncTemplate"
              :hugeIcon="DatabaseSync01Icon"
            >
              {{ $t('themes.template.sync') }}
            </IluriaButton>
          </div>
        </div>
        
        <!-- More actions menu -->
        <ThemeActionMenu
          :theme="theme"
          :is-current="isCurrent"
          :template-status="templateStatus"
          :editable-text-ref="themeNameEditableRef"
          @action="handleAction"
        />
      </div>
      
      <!-- Backup actions -->
      <div class="backup-actions" v-if="!theme.isDefault">
        <IluriaButton
          variant="ghost"
          size="small"
          :huge-icon="DataRecoveryIcon"
          @click="handleCreateBackup"
          :loading="isCreatingBackup"
        >
          {{ $t('themes.backup.create') }}
        </IluriaButton>
        
        <IluriaButton
          variant="ghost"
          size="small"
          :huge-icon="WorkHistoryIcon"
          @click="handleViewBackups"
        >
          {{ $t('themes.backup.history') }}
        </IluriaButton>
      </div>
      
      <!-- Theme meta info -->
      <div class="theme-meta">
        <div class="meta-item">
          <HugeiconsIcon :icon="UserIcon" class="meta-icon" />
          <span>{{ theme.author || 'Iluria' }}</span>
        </div>
        
        <div class="meta-item" v-if="theme.version">
          <HugeiconsIcon :icon="TagIcon" class="meta-icon" />
          <span>v{{ theme.version }}</span>
        </div>
        
        <div class="meta-item" v-if="theme.createdAt">
          <HugeiconsIcon :icon="CalendarIcon" class="meta-icon" />
          <span>{{ formatDate(theme.createdAt) }}</span>
        </div>
        
        <!-- Template info -->
        <div v-if="templateFileCount > 0" class="meta-item">
          <HugeiconsIcon :icon="FileIcon" class="meta-icon" />
          <span>{{ $t('themes.template.files', { count: templateFileCount }) }}</span>
        </div>

        <div v-if="templateVersionDisplay" class="meta-item">
          <HugeiconsIcon :icon="TagIcon" class="meta-icon" />
          <span>{{ $t('themes.template.version') }} {{ templateVersionDisplay.version }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  PaintBoardIcon,
  MaximizeIcon,
  CrownIcon,
  CheckmarkSquare02Icon,
  UserIcon,
  TagIcon,
  CalendarIcon,
  FileIcon,
  Clock03Icon,
  AlertCircleIcon,
  WorkHistoryIcon,
  DatabaseSync01Icon,
  DataRecoveryIcon,

} from '@hugeicons-pro/core-stroke-standard'

import IluriaButton from '@/components/iluria/IluriaButton.vue'
import InlineEditableText from '@/components/utils/InlineEditableText.vue'
import ThemeActionMenu from './ThemeActionMenu.vue'
import { useThemePreview } from '@/composables/useThemePreview'
import { themeService } from '@/services/themeService'
import { useThemeManager } from '@/composables/useThemeManager'
import { useThemeBackup } from '@/composables/useThemeBackup'

const { t } = useI18n()

// Props
const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  isCurrent: {
    type: Boolean,
    default: false
  },
  viewMode: {
    type: String,
    default: 'grid', // 'grid' | 'list'
    validator: (value) => ['grid', 'list'].includes(value)
  },
  autoPreview: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'select',
  'preview', 
  'customize',
  'action'
])

// Composables
const { getThemePreviewUrl, isPreviewAvailable } = useThemePreview()
const { isThemeChanging, themeProgress, isConnected } = useThemeManager()
const { createManualBackup, isCreatingBackup } = useThemeBackup()

// State
const showPreview = ref(true) // Sempre mostrar preview por padrão
const isLoading = ref(false)
const isSelecting = ref(false)
const themeNameEditableRef = ref(null)

// Computed para verificar se este tema está sendo alterado
const isThisThemeChanging = computed(() => {
  return isThemeChanging.value && selectedThemeForChange.value?.id === props.theme.id
})

// Reactive para rastrear qual tema está sendo alterado
const selectedThemeForChange = ref(null)

// Computed para mensagem de progresso
const progressMessage = computed(() => {
  if (!isThisThemeChanging.value || !themeProgress.value.message) {
    return ''
  }
  return themeProgress.value.message
})

// Computed
const previewUrl = computed(() => {
  if (!showPreview.value || !isPreviewAvailable(props.theme)) {
    return null
  }

  try {
    const url = getThemePreviewUrl(props.theme)
    if (!url) {
      console.warn('🚨 No preview URL generated for theme:', props.theme.name)
      return null
    }
    return url
  } catch (error) {
    console.error('🚨 Error generating preview URL for theme:', props.theme.name, error)
    return null
  }
})



// Template status computed
const templateStatus = computed(() => {
  const theme = props.theme

  // Check if theme has template configuration
  if (!theme.hasTemplateFiles && !theme.templateS3Path) {
    return null // No template support
  }

  // Template available and synced
  if (theme.hasTemplateFiles && theme.templateVersion && theme.templateUpdatedAt) {
    return {
      text: t('themes.template.available'),
      icon: CheckmarkSquare02Icon,
      class: 'template-badge--available'
    }
  }

  // Template available but no detailed version info
  if (theme.hasTemplateFiles && theme.templateS3Path) {
    return {
      text: t('themes.template.synced'),
      icon: DatabaseSync01Icon,
      class: 'template-badge--synced'
    }
  }

  // Template configured but not synced
  if (theme.templateS3Path && !theme.hasTemplateFiles) {
    return {
      text: t('themes.template.pending'),
      icon: Clock03Icon,
      class: 'template-badge--pending'
    }
  }

  // Template error or missing
  return {
    text: t('themes.template.error'),
    icon: AlertCircleIcon,
    class: 'template-badge--error'
  }
})

// Template file count
const templateFileCount = computed(() => {
  const theme = props.theme
  if (!theme.templateFiles) {
    return 0
  }

  // Handle array format (most common)
  if (Array.isArray(theme.templateFiles)) {
    return theme.templateFiles.length
  }

  // Handle Set format (from backend LinkedHashSet)
  if (theme.templateFiles instanceof Set) {
    return theme.templateFiles.size
  }

  // Handle object format (fallback)
  if (typeof theme.templateFiles === 'object') {
    return Object.keys(theme.templateFiles).length
  }

  return 0
})

// Template version display
const templateVersionDisplay = computed(() => {
  const theme = props.theme
  if (!theme.templateVersion) return null

  return {
    version: theme.templateVersion,
    updatedAt: theme.templateUpdatedAt ? new Date(theme.templateUpdatedAt) : null
  }
})

// Computed para verificar se o tema pode ser editado
const canEdit = computed(() => {
  // Só pode editar temas customizados ou não padrão
  return !props.theme.isDefault || (props.theme.storeId && props.theme.storeId !== null)
})

// Methods
const openFullPreview = () => {
  emit('preview', props.theme)
}

const selectTheme = async () => {
 
  
  if (isThemeChanging.value) {
  
    return
  }
  
  selectedThemeForChange.value = props.theme
  isSelecting.value = true
  
  try {
   
    emit('select', props.theme)
  } finally {
    isSelecting.value = false
  }
}

const useDefaultTheme = async () => {
  if (isThemeChanging.value) {
    return
  }
  
  selectedThemeForChange.value = props.theme
  isSelecting.value = true
  
  try {
    emit('use-default', props.theme)
  } finally {
    isSelecting.value = false
  }
}

const customizeTheme = () => {
  emit('customize', props.theme)
}

const previewTheme = () => {
  emit('preview', props.theme)
}

const handleAction = (action) => {
  // Se action é um objeto com type, usar ele diretamente
  // Se action é uma string, criar objeto
  const actionData = typeof action === 'string' ? { type: action } : action

  emit('action', actionData, props.theme)
}

// Métodos para edição do nome do tema
const handleThemeNameSave = async (newName) => {
  try {
    // Emitir evento para o componente pai lidar com a atualização
    emit('action', { type: 'rename', newName }, props.theme)
  } catch (error) {
    console.error('Error saving theme name:', error)
  }
}

const handleValidationError = (message) => {
  console.error('Validation error:', message)
}



// Template methods
const syncTemplate = async () => {
  try {
    await themeService.syncTemplateFiles(props.theme.id)
    // Emit success event to parent
    emit('action', { type: 'template-synced', theme: props.theme })
  } catch (error) {
    console.error('Error syncing template:', error)
    // Emit error event to parent
    emit('action', { type: 'template-error', theme: props.theme, error })
  }
}

// Backup methods
const handleCreateBackup = async () => {
  try {
    await createManualBackup(props.theme.id, props.theme.name)
  } catch (error) {
    console.error('Error creating backup:', error)
  }
}

const handleViewBackups = () => {
  emit('action', { type: 'view-backups' }, props.theme)
}

const onPreviewLoad = () => {
  isLoading.value = false
}

const onPreviewError = (error) => {
  console.warn('🚨 Preview iframe error:', error)
  isLoading.value = false
  // Não mostrar preview se houver erro
  showPreview.value = false
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric'
  }).format(date)
}

// Lifecycle
onMounted(() => {
  if (showPreview.value) {
    isLoading.value = true
  }
})
</script>

<style scoped>
.theme-card {
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 16px;
  overflow: visible;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.theme-card:hover {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px var(--iluria-color-primary-light);
  transform: translateY(-4px) scale(1.02);
}

.theme-card--current {
  border-color: var(--iluria-color-success);
  box-shadow: 0 0 0 2px var(--iluria-color-success-light), 0 8px 25px -5px rgba(34, 197, 94, 0.3);
  background: linear-gradient(135deg, var(--iluria-color-surface) 0%, rgba(34, 197, 94, 0.02) 100%);
}

.theme-card--premium {
  background: linear-gradient(135deg, 
    var(--iluria-color-surface) 0%, 
    rgba(255, 215, 0, 0.08) 100%
  );
  border-color: rgba(255, 215, 0, 0.3);
  position: relative;
}

.theme-card--premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  z-index: 1;
}

.theme-card--list {
  display: flex;
  flex-direction: row;
}

.theme-card--list .theme-preview {
  flex: 0 0 300px;
  aspect-ratio: auto;
  min-height: 200px;
}

.theme-card--list .theme-info {
  flex: 1;
}

/* Preview Section */
.theme-preview {
  position: relative;
  aspect-ratio: 16/12;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  overflow: hidden;
  min-height: 300px;
  border-radius: 16px 16px 0 0;
}

.preview-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.preview-iframe {
  width: 125%;
  height: 125%;
  border: none;
  transform: scale(0.8);
  transform-origin: top left;
  pointer-events: none;
  overflow: hidden;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-secondary);
}

.preview-placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.preview-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin-bottom: 0.5rem;
}

.preview-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: currentColor;
  opacity: 0.8;
}

.preview-loading {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.theme-progress-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  z-index: 20;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
  max-width: 280px;
}

.progress-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.progress-info {
  width: 100%;
}

.progress-message {
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--iluria-color-border);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--iluria-color-primary), var(--iluria-color-primary-hover));
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-percentage {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  font-weight: 600;
}

/* WebSocket Status Indicator */
.websocket-status {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: rgba(251, 113, 133, 0.1);
  border-radius: 0.375rem;
  border: 1px solid rgba(251, 113, 133, 0.2);
}

.websocket-status.websocket-connected {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
}

.websocket-indicator {
  width: 0.5rem;
  height: 0.5rem;
  background: #fb7185;
  border-radius: 50%;
  animation: pulse-disconnected 2s infinite;
}

.websocket-connected .websocket-indicator {
  background: #22c55e;
  animation: pulse-connected 2s infinite;
}

.websocket-text {
  font-size: 0.6875rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
}

@keyframes pulse-connected {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes pulse-disconnected {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.4; }
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.preview-controls {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(10px);
}

.theme-card:hover .preview-controls {
  opacity: 1;
  transform: translateY(0);
}

.premium-badge {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #8b5a00;
  padding: 0.35rem 0.85rem;
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  animation: premium-glow 2s ease-in-out infinite alternate;
}

@keyframes premium-glow {
  0% {
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6), 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.current-badge {
  position: absolute;
  bottom: 0.75rem;
  left: 0.75rem;
  background: linear-gradient(135deg, var(--iluria-color-success) 0%, #059669 100%);
  color: white;
  padding: 0.35rem 0.85rem;
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.template-badge {
  position: absolute;
  bottom: 0.75rem;
  right: 0.75rem;
  padding: 0.35rem 0.85rem;
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  backdrop-filter: blur(12px);
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.template-badge:hover {
  transform: scale(1.05);
}

.template-badge--available {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(16, 185, 129, 0.95) 100%);
  color: white;
  border: 1px solid rgba(34, 197, 94, 0.4);
}

.template-badge--pending {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.95) 0%, rgba(245, 158, 11, 0.95) 100%);
  color: white;
  border: 1px solid rgba(251, 191, 36, 0.4);
  animation: pending-pulse 2s ease-in-out infinite;
}

@keyframes pending-pulse {
  0%, 100% {
    opacity: 0.9;
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.template-badge--synced {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(37, 99, 235, 0.95) 100%);
  color: white;
  border: 1px solid rgba(59, 130, 246, 0.4);
}

.template-badge--error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 0.95) 100%);
  color: white;
  border: 1px solid rgba(239, 68, 68, 0.4);
  animation: error-shake 0.5s ease-in-out;
}

@keyframes error-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Theme Info */
.theme-info {
  padding: 1.75rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.01) 100%);
}

.theme-header {
  margin-bottom: 0.5rem;
}

.theme-card .theme-name,
.theme-card .theme-name.editable-text,
.theme-card h3.theme-name,
.theme-card h3.theme-name.editable-text {
  font-size: 1.6rem !important;
  font-weight: 700 !important;
  color: var(--iluria-color-text-primary) !important;
  margin-bottom: 0.5rem !important;
  line-height: 1.3 !important;
  letter-spacing: -0.01em !important;
}

.theme-description {
  color: var(--iluria-color-text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  font-weight: 400;
}

/* Tags */
.theme-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  margin: 0.5rem 0;
}

.tag {
  padding: 0.3rem 0.8rem;
  background: linear-gradient(135deg, var(--iluria-color-background) 0%, rgba(var(--iluria-color-primary-rgb), 0.05) 100%);
  border: 1px solid var(--iluria-color-border);
  border-radius: 25px;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: default;
}

.tag:hover {
  background: linear-gradient(135deg, var(--iluria-color-primary-light) 0%, rgba(var(--iluria-color-primary-rgb), 0.1) 100%);
  border-color: var(--iluria-color-primary);
  color: var(--iluria-color-primary);
  transform: translateY(-1px);
}

.tag--more {
  background: linear-gradient(135deg, var(--iluria-color-primary) 0%, var(--iluria-color-primary-hover) 100%);
  color: white;
  border-color: var(--iluria-color-primary);
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(var(--iluria-color-primary-rgb), 0.3);
}

/* Actions */
.theme-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-top: auto;
  position: relative;
  z-index: 10;
  padding-top: 0.5rem;
  border-top: 1px solid var(--iluria-color-border);
}

.primary-actions {
  display: flex;
  gap: 0.85rem;
  flex: 1;
  flex-wrap: wrap;
  align-items: center;
}

.template-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.backup-actions {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem 1rem 0;
  border-top: 1px solid var(--iluria-color-border);
  margin-top: 0.75rem;
}

/* Meta Info */
.theme-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--iluria-color-border);
  margin-top: 0.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--iluria-color-text-secondary);
  font-size: 0.75rem;
}

.meta-icon {
  width: 1rem;
  height: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .theme-card--list {
    flex-direction: column;
  }
  
  .theme-card--list .theme-preview {
    flex: none;
    aspect-ratio: 16/10;
  }
  
  .primary-actions {
    flex-direction: column;
  }
  
  .theme-meta {
    justify-content: center;
  }

  .theme-card .theme-name,
  .theme-card .theme-name.editable-text,
  .theme-card h3.theme-name,
  .theme-card h3.theme-name.editable-text {
    font-size: 1.4rem !important; /* Menor em mobile */
  }
}
</style>

<style>
/* Regra global para garantir que o tamanho da fonte seja aplicado nos cards da biblioteca */
.theme-card .theme-name,
.theme-card .theme-name.editable-text,
.theme-card h3.theme-name,
.theme-card h3.theme-name.editable-text,
.theme-card [class*="theme-name"],
.theme-card h3[class*="theme-name"] {
  font-size: 1.6rem !important;
  font-weight: 700 !important;
  color: var(--iluria-color-text-primary) !important;
  margin-bottom: 0.5rem !important;
  line-height: 1.3 !important;
  letter-spacing: -0.01em !important;
}

/* Força aplicação em elementos inline-editable */
.theme-card .inline-editable .editable-text.theme-name,
.theme-card .inline-editable h3.editable-text.theme-name {
  font-size: 1.6rem !important;
  font-weight: 700 !important;
}

@media (max-width: 768px) {
  .theme-card .theme-name,
  .theme-card .theme-name.editable-text,
  .theme-card h3.theme-name,
  .theme-card h3.theme-name.editable-text {
    font-size: 1.4rem !important; /* Menor em mobile */
  }
}
</style>