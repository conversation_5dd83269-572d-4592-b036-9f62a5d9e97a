<template>
    <div class="team-management-container">
        <!-- Header Section -->
        <IluriaHeader
            :title="t('team.teamManagement.title')"
            :subtitle="t('team.teamManagement.subtitle')"
            :showSearch="false"
            :showAdd="true"
            :addText="t('team.teamManagement.inviteCollaborator')"
            :addIcon="UserAdd01Icon"
            :customButtons="headerCustomButtons"
            @add-click="openInviteModal"
            @custom-click="handleCustomButtonClick"
        />

        <!-- Main Content -->
        <div class="table-wrapper">
            <IluriaDataTable
                :value="teamMembers"
                :columns="teamTableColumns"
                :loading="loading"
                dataKey="id"
                class="team-table iluria-data-table"
            >
                <!-- Column Headers -->
                <template #header-avatar>
                    <span class="column-header">{{ t('team.teamManagement.columns.user') }}</span>
                </template>
                <template #header-name>
                    <span class="column-header" data-sortable="true" @click="toggleSort('name')">
                        {{ t('team.teamManagement.columns.name') }}
                        <span v-if="sortField === 'name'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                    </span>
                </template>
                <template #header-email>
                    <span class="column-header" data-sortable="true" @click="toggleSort('email')">
                        {{ t('team.teamManagement.columns.email') }}
                        <span v-if="sortField === 'email'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                    </span>
                </template>
                <template #header-role>
                    <span class="column-header">{{ t('team.teamManagement.columns.role') }}</span>
                </template>

                <template #header-actions>
                    <span class="column-header">{{ t('team.teamManagement.columns.actions') }}</span>
                </template>

                <!-- Column Templates -->
                <template #column-avatar="{ data }">
                    <div class="member-avatar-cell">
                        <div class="member-avatar-container">
                            <img
                                v-if="data.profilePictureUrl"
                                :src="data.profilePictureUrl"
                                alt="User Avatar"
                                class="member-avatar"
                                @error="handleAvatarError($event, data)"
                            />
                            <img
                                v-else
                                :src="getUserProfileImage(data)"
                                alt="User Avatar"
                                class="member-avatar member-avatar-fallback"
                            />
                        </div>
                    </div>
                </template>

                <template #column-name="{ data }">
                    <div class="member-info">
                        <h3 class="member-name" :title="data.fullName || `${data.firstName} ${data.lastName}`">
                            {{ truncateText(data.fullName || `${data.firstName} ${data.lastName}`, 30) }}
                        </h3>
                        <p class="member-joined" v-if="data.joinedAt">
                            {{ t('team.teamManagement.memberInfo.since') }} {{ formatDate(data.joinedAt) }}
                        </p>
                    </div>
                </template>

                <template #column-email="{ data }">
                    <span class="member-email" :title="data.email">{{ data.email }}</span>
                </template>

                <template #column-role="{ data }">
                    <span class="role-badge" :class="getRoleClass(data.roleName)">
                        {{ data.roleName }}
                    </span>
                </template>

                
           

                <template #column-actions="{ data }">
                    <div class="action-buttons">
                        <IluriaButton
                            v-if="!isOwner(data)"
                            color="text-primary"
                            size="small"
                            :hugeIcon="PencilEdit01Icon"
                            @click.prevent="editMemberRole(data)"
                            :title="t('team.teamManagement.actions.editRole')"
                        />


                        <IluriaButton
                            v-if="!isOwner(data)"
                            color="text-danger"
                            size="small"
                            :hugeIcon="Delete01Icon"
                            @click.prevent="confirmRemoveMember(data)"
                            :title="t('team.teamManagement.actions.removeMember')"
                        />
                    </div>
                </template>
                
                <!-- Empty State -->
                <template #empty>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <HugeiconsIcon :icon="UserGroup02Icon" size="48" :strokeWidth="1.5" />
                        </div>
                        <h3 class="empty-title">{{ t('team.teamManagement.emptyState.title') }}</h3>
                        <p class="empty-description">{{ t('team.teamManagement.emptyState.description') }}</p>
                        <IluriaButton @click="openInviteModal" :hugeIcon="UserAdd01Icon" class="mt-4">
                            {{ t('team.teamManagement.inviteCollaborator') }}
                        </IluriaButton>
                    </div>
                </template>

                <!-- Loading State -->
                <template #loading>
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <span>{{ t('team.teamManagement.loadingState.message') }}</span>
                    </div>
                </template>
            </IluriaDataTable>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" v-if="totalPages > 0">
            <IluriaPagination 
                :current-page="currentPage"
                :total-pages="totalPages"
                @go-to-page="changePage"
            />
        </div>

        <!-- Invite Member Modal -->
        <InviteMemberModal
            ref="inviteModal"
            @member-invited="handleMemberInvited"
        />

        <!-- Pending Invites Modal -->
        <IluriaModal
            v-model="showPendingInvitesModal"
            :title="t('team.pendingInvites.title')"
            :subtitle="t('team.pendingInvites.subtitle')"
            :icon="Mail01Icon"
            :showSave="false"
            :showFooter="false"
            :dialogStyle="{ width: '80vw', maxWidth: '1000px' }"
        >
            <div class="pending-invites-content">
                <IluriaDataTable
                    :value="pendingInvites"
                    :columns="pendingInvitesColumns"
                    :loading="loadingPendingInvites"
                    dataKey="id"
                    class="pending-invites-table iluria-data-table"
                >
                    <!-- Column Headers -->
                    <template #header-email>
                        <span class="column-header">{{ t('team.pendingInvites.columns.email') }}</span>
                    </template>
                    <template #header-role>
                        <span class="column-header">{{ t('team.pendingInvites.columns.role') }}</span>
                    </template>
                    <template #header-sentAt>
                        <span class="column-header">{{ t('team.pendingInvites.columns.sentAt') }}</span>
                    </template>
                    <template #header-actions>
                        <span class="column-header">{{ t('team.pendingInvites.columns.actions') }}</span>
                    </template>

                    <!-- Column Templates -->
                    <template #column-email="{ data }">
                        <div class="email-cell">
                            <HugeiconsIcon :icon="Mail01Icon" size="16" :strokeWidth="1.5" />
                            <span>{{ data.inviteEmail || data.email || data.invitedEmail || data.userEmail || 'N/A' }}</span>
                        </div>
                    </template>

                    <template #column-role="{ data }">
                        <span class="role-badge" :class="getRoleClass()">
                            {{ getRoleNameFromId(data.roleId) }}
                        </span>
                    </template>

                    <template #column-sentAt="{ data }">
                        <div class="date-cell">
                            <span class="date-text">{{ formatDate(data.sentAt || data.createdAt || data.invitedAt) }}</span>
                            <span class="time-ago">{{ getTimeAgo(data.sentAt || data.createdAt || data.invitedAt) }}</span>
                        </div>
                    </template>

                    <template #column-actions="{ data }">
                        <div class="action-buttons">
                            <IluriaButton
                                color="danger"
                                size="small"
                                variant="outline"
                                :hugeIcon="Cancel01Icon"
                                @click.prevent="confirmCancelInvite(data)"
                                :title="t('team.pendingInvites.actions.cancel')"
                                :loading="data.cancelling"
                            >
                                {{ t('team.pendingInvites.actions.cancel') }}
                            </IluriaButton>
                        </div>
                    </template>

                    <!-- Empty State -->
                    <template #empty>
                        <div class="empty-state">
                            <div class="empty-icon">
                                <HugeiconsIcon :icon="Mail01Icon" size="48" :strokeWidth="1.5" />
                            </div>
                            <h3 class="empty-title">{{ t('team.pendingInvites.emptyState.title') }}</h3>
                            <p class="empty-description">{{ t('team.pendingInvites.emptyState.description') }}</p>
                        </div>
                    </template>

                    <!-- Loading State -->
                    <template #loading>
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            <span>{{ t('team.pendingInvites.loadingState.message') }}</span>
                        </div>
                    </template>
                </IluriaDataTable>
            </div>
        </IluriaModal>

        <!-- Confirmation Dialog -->
        <IluriaConfirmationModal 
            :isVisible="showConfirmDialog"
            :title="confirmData.title" 
            :message="confirmData.message"
            :type="confirmData.type || 'error'"
            @confirm="handleConfirm"
            @cancel="showConfirmDialog = false"
        />
    </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { teamApi } from '@/services/team.service'
import { roleApi } from '@/services/role.service'
import { ref, onMounted, computed } from 'vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import {
    Delete01Icon,
    PencilEdit01Icon,
    UserAdd01Icon,
    UserGroup02Icon,
    Mail01Icon,
    Cancel01Icon
} from '@hugeicons-pro/core-stroke-rounded'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import InviteMemberModal from '@/components/team/InviteMemberModal.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import { useToast } from '@/services/toast.service'
import { useRouter } from 'vue-router'
import { useStoreStore } from '@/stores/store.store'
import { generateUserAvatar, handleImageError } from '@/utils/avatarUtils.js'

const { t } = useI18n()
const inviteModal = ref(null)

// Confirmation dialog state
const showConfirmDialog = ref(false)
const confirmData = ref({ title: '', message: '', type: 'error', onConfirm: null })
const toast = useToast()
const router = useRouter()
const storeStore = useStoreStore()

// Table columns configuration
const teamTableColumns = computed(() => [
    { field: 'avatar', headerClass: 'col-avatar', class: 'col-avatar' },
    { field: 'name', headerClass: 'col-flex', class: 'col-flex' },
    { field: 'email', headerClass: 'col-email', class: 'col-email' },
    { field: 'role', headerClass: 'col-role', class: 'col-role' },
    { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
])

// Reactive data
const teamMembers = ref([])
const loading = ref(true)
const currentPage = ref(0)
const totalPages = ref(0)
const totalElements = ref(0)
const sortField = ref(null)
const sortOrder = ref(null)
const filters = ref({
    search: ''
})

// Pending invites modal data
const showPendingInvitesModal = ref(false)
const pendingInvites = ref([])
const loadingPendingInvites = ref(false)

// Store roles data
const storeRoles = ref([])
const loadingRoles = ref(false)

// Pending invites table columns
const pendingInvitesColumns = computed(() => [
    { field: 'email', headerClass: 'col-email', class: 'col-email' },
    { field: 'role', headerClass: 'col-role', class: 'col-role' },
    { field: 'sentAt', headerClass: 'col-date', class: 'col-date' },
    { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
])

// Header custom buttons configuration
const headerCustomButtons = computed(() => [
    {
        text: t('team.teamManagement.roles'),
        icon: UserGroup02Icon,
        color: 'primary',
        variant: 'outline',
        tooltip: t('team.teamManagement.rolesDescription')
    },
    {
        text: t('team.teamManagement.invites'),
        icon: Mail01Icon,
        color: 'secondary',
        variant: 'outline',
        tooltip: t('team.teamManagement.invitesDescription')
    }
])
const getUserProfileImage = (user) => {
    const userName = user?.fullName ||
                     (user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : '') ||
                     user?.firstName ||
                     user?.name ||
                     '';
    return generateUserAvatar(userName, 48, 'square');
};

const handleAvatarError = (event, user) => {
    const userName = user?.fullName ||
                     (user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : '') ||
                     user?.firstName ||
                     user?.name ||
                     '';
    handleImageError(event, userName, 'user', 'square', 48);
};

// Methods
const loadTeamMembers = async () => {
    loading.value = true
    try {
        const params = {
            page: currentPage.value,
            size: 10,
            search: filters.value.search
        }
        
        const response = await teamApi.getTeamMembers(params)
        
        // Se response.data é um array, use diretamente; se é um objeto com content, use content
        if (Array.isArray(response.data)) {
            teamMembers.value = response.data
            totalPages.value = response.data.length > 10 ? Math.ceil(response.data.length / 10) : 0
            totalElements.value = response.data.length
        } else {
            teamMembers.value = response.data.content || []
            totalPages.value = response.data.page?.totalPages || response.data.totalPages || 0
            totalElements.value = response.data.page?.totalElements || response.data.totalElements || 0
        }
        
    } catch (error) {
        console.error('Error loading team members:', error)
        toast.showError(t('team.teamManagement.messages.loadError'))
    } finally {
        loading.value = false
    }
}

const loadStoreRoles = async () => {
    loadingRoles.value = true
    try {
        const storeId = storeStore.selectedStore?.id
        if (!storeId) {
            console.warn('No store selected')
            return
        }

        const response = await roleApi.getStoreRoles(storeId)
        storeRoles.value = response.data || []
    } catch (error) {
        console.error('Error loading store roles:', error)
        // Don't show error toast as this is not critical for the page functionality
    } finally {
        loadingRoles.value = false
    }
}

const changePage = (page) => {
    currentPage.value = page
    loadTeamMembers()
}

const toggleSort = (field) => {
    if (sortField.value === field) {
        sortOrder.value *= -1
    } else {
        sortField.value = field
        sortOrder.value = 1
    }
    // Apply sorting logic here if needed
}

let searchTimeout = null
const debouncedSearch = () => {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
        currentPage.value = 0
        loadTeamMembers()
    }, 400)
}

const handleSearch = (searchValue) => {
    filters.value.search = searchValue
    debouncedSearch()
}

// Modal and actions
const openInviteModal = () => {
    inviteModal.value?.open()
}

const handleCustomButtonClick = (index, button) => {
    if (index === 0) { // Configuração de Cargos
        router.push('/team/roles')
    } else if (index === 1) { // Lista de Convites Enviados
        openPendingInvitesModal()
    }
}

const handleMemberInvited = () => {
    // Reload team members when a new invitation is sent
    loadTeamMembers()
    // Reload store roles in case new roles were created
    loadStoreRoles()
    // Reload pending invites if modal is open
    if (showPendingInvitesModal.value) {
        loadPendingInvites()
    }
}

// Pending invites methods
const openPendingInvitesModal = () => {
    showPendingInvitesModal.value = true
    loadPendingInvites()
}

const loadPendingInvites = async () => {
    loadingPendingInvites.value = true
    try {
        const response = await teamApi.getPendingInvitations()
        pendingInvites.value = response.data || []
    } catch (error) {
        console.error('Error loading pending invites:', error)
        toast.showError(t('team.pendingInvites.messages.loadError'))
        pendingInvites.value = []
    } finally {
        loadingPendingInvites.value = false
    }
}

const confirmCancelInvite = (invite) => {
    confirmData.value = {
        title: t('team.pendingInvites.confirmCancel.title'),
        message: t('team.pendingInvites.confirmCancel.message', { email: invite.email }),
        type: 'error',
        onConfirm: () => cancelInvite(invite)
    }
    showConfirmDialog.value = true
}

const cancelInvite = async (invite) => {
    const inviteIndex = pendingInvites.value.findIndex(i => i.id === invite.id)
    if (inviteIndex !== -1) {
        pendingInvites.value[inviteIndex].cancelling = true
    }

    try {
        await teamApi.cancelInvitation(invite.id)
        toast.showSuccess(t('team.pendingInvites.messages.cancelSuccess'))

        // Remove the invite from the list
        pendingInvites.value = pendingInvites.value.filter(i => i.id !== invite.id)

    } catch (error) {
        console.error('Error cancelling invite:', error)
        toast.showError(t('team.pendingInvites.messages.cancelError'))

        if (inviteIndex !== -1) {
            pendingInvites.value[inviteIndex].cancelling = false
        }
    }
}

const editMemberRole = (member) => {
    // TODO: Open role edit modal
}

const confirmRemoveMember = (member) => {
    const memberName = member.fullName || `${member.firstName} ${member.lastName}` || member.email
    confirmData.value = {
        title: t('team.teamManagement.confirmRemoval.title'),
        message: t('team.teamManagement.confirmRemoval.message', { memberName }),
        type: 'error',
        onConfirm: () => removeMember(member.userId)
    }
    showConfirmDialog.value = true
}





const removeMember = async (memberId) => {
    try {
        await teamApi.removeMember(memberId)
        toast.showSuccess(t('team.teamManagement.messages.removeSuccess'))
        loadTeamMembers()
    } catch (error) {
        console.error('Error removing member:', error)
        toast.showError(t('team.teamManagement.messages.removeError'))
    }
}



// Utility functions
const truncateText = (text, maxLength) => {
    if (!text) return ''
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
}

const formatDate = (dateString) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('pt-BR')
}


const getTimeAgo = (dateString) => {
    if (!dateString) return ''

    const now = new Date()
    const date = new Date(dateString)
    const diffInSeconds = Math.floor((now - date) / 1000)

    if (diffInSeconds < 60) return t('team.pendingInvites.timeAgo.now')
    if (diffInSeconds < 3600) return t('team.pendingInvites.timeAgo.minutesAgo', { minutes: Math.floor(diffInSeconds / 60) })
    if (diffInSeconds < 86400) return t('team.pendingInvites.timeAgo.hoursAgo', { hours: Math.floor(diffInSeconds / 3600) })

    const days = Math.floor(diffInSeconds / 86400)
    return t('team.pendingInvites.timeAgo.daysAgo', { days })
}

const getRoleClass = (roleName) => {
    // Simplified role styling - could be enhanced based on role names
    return 'role-default'
}

const getRoleNameFromId = (roleId) => {
    if (!roleId) return 'N/A'
    
    const role = storeRoles.value.find(r => r.id === roleId)
    return role ? role.name : 'Cargo não encontrado'
}

const isOwner = (member) => {
    // For now, we'll need to identify owners differently
    // This could be based on role name or a separate field
    return member.roleName === 'Proprietário' || member.roleName === 'Owner'
}


// Confirmation dialog handler
const handleConfirm = () => {
    if (confirmData.value.onConfirm) {
        confirmData.value.onConfirm()
    }
    showConfirmDialog.value = false
}

// Lifecycle
onMounted(() => {
    loadTeamMembers()
    loadStoreRoles()
})
</script>

<style scoped>
.team-management-container {
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Table Styles */
.table-wrapper {
    margin-bottom: 24px;
}

.member-avatar {
    width: 48px;
    height: 48px;
    border-radius: 8px; /* Quadrado com bordas arredondadas */
    object-fit: cover;
    transition: all 0.3s ease;
}

.member-avatar-fallback {
    border-radius: 8px; /* Mantém quadrado para fallback */
}

.member-avatar-container {
    width: 48px;
    height: 48px;
    border-radius: 8px; /* Quadrado com bordas arredondadas */
    background: var(--iluria-color-primary);
    color: var(--iluria-color-button-primary-fg);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
    overflow: hidden; /* Garante que a imagem não saia do container */
}

/* Column width definitions */
:deep(.col-avatar) { width: 80px; }
:deep(.col-flex) { width: auto; }
:deep(.col-email) { width: 200px; }
:deep(.col-role) { width: 140px; }
:deep(.col-actions) { width: 160px; }

/* General table styling */
:deep(.team-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.team-table .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.team-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: center;
}

:deep(.team-table .p-datatable-thead > tr > th.col-flex) {
  text-align: center;
}

:deep(.team-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.team-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.team-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  text-align: center;
}

:deep(.team-table .p-datatable-tbody > tr > td.col-flex) {
  text-align: center;
}

/* Column header style */
:deep(.team-table th .column-header) {
  display: block;
  width: 100%;
  text-align: inherit;
  cursor: default;
  user-select: none;
}

:deep(.team-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

/* Member Avatar */
.member-avatar-cell {
    display: flex;
    justify-content: center;
}

/* Duplicated styles removed - using the one above */

.member-avatar-container:hover {
    transform: scale(1.05);
    box-shadow: var(--iluria-shadow-md);
}

/* Member Info */
.member-info {
    min-width: 0;
    max-width: 100%;
    overflow: hidden;
    text-align: center;
}

.member-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 4px 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.3s ease;
}

.member-joined {
    font-size: 12px;
    color: var(--iluria-color-text-secondary);
    margin: 0;
    line-height: 1.4;
    transition: color 0.3s ease;
}

/* Email */
.member-email {
    font-size: 13px;
    color: var(--iluria-color-text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    max-width: 180px;
    text-align: center;
    margin: 0 auto;
}

/* Role Badges */
.role-badge {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
    text-align: center;
    white-space: nowrap;
    display: inline-block;
}

.role-owner {
    background: #dcfce7;
    color: #166534;
}

.role-admin {
    background: #fef3c7;
    color: #d97706;
}

.role-designer {
    background: #e0e7ff;
    color: #3730a3;
}

.role-marketer {
    background: #f3e8ff;
    color: #7c3aed;
}

.role-default {
    background: var(--iluria-color-sidebar-bg);
    color: var(--iluria-color-text-muted);
}



/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.action-buttons .btn {
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    background: var(--iluria-color-surface);
    transition: background-color 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.empty-icon {
    color: var(--iluria-color-text-muted);
    margin-bottom: 16px;
    transition: color 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.empty-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 8px 0;
    transition: color 0.3s ease;
    text-align: center;
}

.empty-description {
    font-size: 14px;
    color: var(--iluria-color-text-secondary);
    margin: 0 0 16px 0;
    transition: color 0.3s ease;
    text-align: center;
    line-height: 1.5;
    max-width: 400px;
}

/* Loading State */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 32px;
    color: var(--iluria-color-text-secondary);
    font-size: 14px;
    background: var(--iluria-color-surface);
    transition: all 0.3s ease;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
}

/* Pending Invites Modal */
.pending-invites-content {
    min-height: 400px;
    margin: 0;
    padding: 0;
}

/* Remove padding from modal content */
:deep(.iluria-modal .p-dialog-content) {
    padding: 0 !important;
}

:deep(.pending-invites-table) {
    border-radius: 8px;
    box-shadow: var(--iluria-shadow-sm);
    background: var(--iluria-color-surface) !important;
    border: 1px solid var(--iluria-color-border) !important;
}

:deep(.pending-invites-table .p-datatable-thead > tr > th) {
    background: var(--iluria-color-sidebar-bg) !important;
    color: var(--iluria-color-text-primary) !important;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--iluria-color-border) !important;
    padding: 16px;
    text-align: center;
}

:deep(.pending-invites-table .p-datatable-tbody > tr > td) {
    padding: 16px;
    border-bottom: 1px solid var(--iluria-color-border);
    vertical-align: middle;
    font-size: 14px;
    text-align: center;
}

/* Column header style for pending invites */
:deep(.pending-invites-table th .column-header) {
    display: block;
    width: 100%;
    text-align: center;
    cursor: default;
    user-select: none;
}

/* Email cell */
.email-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--iluria-color-text-primary);
}

/* Date cell */
.date-cell {
    text-align: center;
}

.date-text {
    display: block;
    font-size: 13px;
    color: var(--iluria-color-text-primary);
    font-weight: 500;
    margin-bottom: 2px;
}

.time-ago {
    display: block;
    font-size: 11px;
    color: var(--iluria-color-text-secondary);
}



/* Centralize role badge in pending invites table */
:deep(.pending-invites-table .col-role) {
    text-align: center;
    width: 140px; 
}

:deep(.pending-invites-table .col-role .role-badge) {
    margin: 0 auto;
}

/* Column width definitions for pending invites */
:deep(.pending-invites-table .col-email) { width: auto; }
:deep(.pending-invites-table .col-date) { width: 140px; }
:deep(.pending-invites-table .col-actions) { width: 120px; }

/* Responsive */
@media (max-width: 768px) {
    .team-management-container {
        padding: 16px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
    
    :deep(.team-table .p-datatable-tbody > tr > td) {
        padding: 12px 8px;
    }
    
    :deep(.team-table .p-datatable-thead > tr > th) {
        padding: 12px 8px;
        font-size: 11px;
    }
    
    .member-email {
        max-width: 120px;
    }
}
</style>
