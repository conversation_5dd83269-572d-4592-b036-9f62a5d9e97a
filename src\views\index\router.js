import { createRouter, createWebHistory } from "vue-router";
import { useAuthStore } from "@/stores/auth.store";
import { useStoreStore } from "@/stores/store.store";
import tokenManager from "@/services/tokenManager.service";
import { useGlobalPermissions } from "@/composables/usePermissions";
import { PERMISSIONS } from "@/constants/permissions";

import Login from "@/views/login/Login.vue";
import ForgotPassword from "@/views/login/ForgotPassword.vue";
import PasswordRecovery from "@/views/login/PasswordRecovery.vue";
import DashboardShell from "@/components/layout/DashboardShell.vue";
import NewCustomerView from "@/views/customer/NewCustomer.vue";
import NewProductView from "@/views/product/NewProduct.vue";
import ProductListView from "@/views/product/ProductListView.vue";
import UserChangePassword from "@/views/users/UserChangePassword.vue";
import UserSettings from "@/views/users/UserSettings.vue";
import HomeView from "@/views/home/<USER>";
import CategoryManagerView from "@/views/product/CategoryManager.vue";
import SignUp from "@/views/login/SignUp.vue";
import RequestSignUp from "@/views/login/RequestSignUp.vue";
import StoreSelection from "@/views/stores/StoreSelection.vue";
import MinimumOrderView from "@/views/settings/MinimumOrder.vue";
import ShippingConfigView from "@/views/settings/ShippingConfig.vue";
import CustomerListView from "@/views/customer/CustomerListView.vue";
import AddressForm from "@/components/customer/AddressForm.vue";
import CouponManagerView from "@/views/coupon/CouponManager.vue";
import CouponForm from "@/views/coupon/CouponForm.vue";
import CategorySeoConfigView from "@/views/product/CategorySeoConfig.vue";
import AttributesManagerView from "@/views/product/AttributesManager.vue";
import StoreSeoSettingsView from "@/views/settings/StoreSeoSettings.vue";
import SocialMediaSettings from "@/views/settings/SocialMediaSettings.vue";
import StoreBrandAssets from "@/views/settings/StoreBrandAssets.vue";
import CommunityManagement from "@/views/marketing/community/CommunityManagement.vue";

import PagesListView from "@/views/pages/PagesListView.vue";
import PageEditor from "@/views/pages/PageEditor.vue";
import DomainUrlList from "@/views/settings/DomainUrlList.vue";
import DomainUrlForm from "@/views/settings/DomainUrlForm.vue";
import DomainUrlValidate from "@/views/settings/DomainUrlValidate.vue";
import OriginCepForm from "@/views/settings/OriginCepForm.vue";
import PromotionsListView from "@/views/promotion/PromotionsListView.vue";
import OrderListView from "@/views/order/OrderListView.vue";
import OrderForm from "@/views/order/OrderForm.vue";
import UrlRedirectList from "@/views/settings/UrlRedirectList.vue";
import UrlRedirectForm from "@/views/settings/UrlRedirectForm.vue";
import FileManager from "@/views/filemanager/FileManager.vue";
import EditorView from "@/views/layouteditor/EditorView.vue";
import CollectionProductList from "@/views/collection/CollectionProductList.vue";
import CollectionProductForm from "@/views/collection/CollectionProductForm.vue";
import LabelInitial from "@/views/labels/LabelInitial.vue";
import CombinedProduct from '@/views/products/CombinedProduct.vue';
import CombinedProductEdit from '@/views/products/CombinedProductEdit.vue';
import MeasurementTablesListView from "@/views/measurementTables/MeasurementTablesListView.vue";
import MeasurementTableEditor from "@/views/measurementTables/MeasurementTableEditor.vue";
import MeasurementTableFormInline from "@/components/measurementTables/MeasurementTableFormInline.vue";
import EmailNotificationSettings from "@/views/settings/EmailNotificationSettings.vue";
import QuestionsAnswers from "@/views/product/QuestionsAnswers.vue";
import QuestionResponse from "@/views/product/QuestionResponse.vue";
import PredefinedAnswersManager from "@/views/product/PredefinedAnswersManager.vue";
import StoreModeView from "@/views/settings/StoreModeView.vue";
import PaymentMethodsView from "@/views/settings/PaymentMethodsView.vue";
import StorePhysicalData from "@/views/settings/StorePhysicalData.vue";
import BlogDashboard from "@/views/blog/BlogDashboard.vue";
import BlogCategoryList from "@/views/blog/BlogCategoryList.vue";
import BlogCategoryForm from "@/views/blog/BlogCategoryForm.vue";
import BlogPostList from "@/views/blog/BlogPostList.vue";
import BlogPostForm from "@/views/blog/BlogPostForm.vue";
import GiftCardView from "../product/GiftCardView.vue";
import NewGiftCardView from "../product/NewGiftCardView.vue";
import ThemesGallery from "@/views/themes/ThemesGallery.vue";
import TestEnvironment from "@/views/test-env/TestEnvironment.vue";
import TeamManagement from "@/views/team/TeamManagement.vue";
import RoleManagement from "@/views/team/RoleManagement.vue";
import NoPermission from "@/components/errors/NoPermission.vue";
import NotFound from "@/components/errors/NotFound.vue";



const routes = [
  { path: "/login", name: "Login", component: Login },
  {
    path: "/forgot-password",
    name: "ForgotPassword",
    component: ForgotPassword,
  },
  {
    path: "/password-recovery",
    name: "PasswordRecovery",
    component: PasswordRecovery,
  },
  {
    path: "/request-signup",
    name: "RequestSignUp",
    component: RequestSignUp,
  },
  {
    path: "/signup",
    name: "SignUp",
    component: SignUp,
  },
  {
    path: "/stores",
    name: "StoreSelection",
    component: StoreSelection,
  },
  {
    path: "/no-permission",
    name: "NoPermission",
    component: NoPermission,
  },
  {
    path: "/user/settings",
    name: "UserSettings",
    component: UserSettings,
  },
  {
    path: "/layout-editor",
    name: "layout-editor",
    component: EditorView,
  },
  {
    path: "/test-env",
    name: "test-environment",
    component: TestEnvironment,
  },

  {
    path: "/",
    component: DashboardShell,
    children: [
      {
        path: "",
        name: "Home",
        component: HomeView,
      },
      {
        path: "team",
        name: "TeamManagement",
        component: TeamManagement,
        meta: { requiredPermissions: [PERMISSIONS.TEAM_VIEW] }
      },
      {
        path: "team/roles",
        name: "RoleManagement",
        component: RoleManagement,
        meta: { requiredPermissions: [PERMISSIONS.TEAM_EDIT] }
      },
      {
        path: "customer-list",
        component: CustomerListView,
        meta: { requiredPermissions: [PERMISSIONS.CUSTOMER_VIEW] }
      },
      {
        path: "customers/import",
        name: "customer-import",
        component: () => import('@/views/customer/CustomerImportView.vue'),
      },
      {
        path: "customers/export",
        name: "customer-export",
        component: () => import('@/views/customer/CustomerExportView.vue'),
      },
      {
        path: "customers/exports",
        name: "customer-exports-list",
        component: () => import('@/views/customer/CustomerExportListView.vue'),
      },
      {
        path: "customers/new",
        component: NewCustomerView,
      },
      {
        path: "/customers/:id",
        name: "EditCustomer",
        component: NewCustomerView,
        props: true,
        meta: { requiredPermissions: [PERMISSIONS.CUSTOMER_EDIT] }
      },
      {
        path: "customers/:id/addresses",
        name: "CustomerAddresses",
        component: AddressForm,
        props: true,
      },
      {
        path: "products",
        component: ProductListView,
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_VIEW] }
      },
      {
        path: "products/new",
        name: "ProductNew",
        component: NewProductView,
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_CREATE] }
      },
      {
        path: "products/:id",
        name: "ProductEdit",
        component: NewProductView,
        props: true,
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_EDIT] }
      },
      {
        path: "products/import",
        name: "product-import",
        component: () => import('@/views/product/ProductImportView.vue'),
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_CREATE] }
      },
      {
        path: "products/export",
        name: "product-export",
        component: () => import('@/views/product/ProductExportView.vue'),
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_VIEW] }
      },
      {
        path: "products/exports",
        name: "product-exports-list",
        component: () => import('@/views/product/ProductExportListView.vue'),
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_VIEW] }
      },
      {
        path: "products/combinado",
        name: "CombinedProduct",
        component: CombinedProduct,
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_CREATE] }
      },
      {
        path: "products/combined/edit/:id?",
        name: "CombinedProductEdit",
        component: CombinedProductEdit,
        props: true,
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_EDIT] }
      },
      {
        path: "product/category-manager",
        component: CategoryManagerView,
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_CATEGORY_MANAGE] }
      },
      {
        path: "product/attributes-manager",
        name: "AttributesManager",
        component: AttributesManagerView,
        meta: { requiredPermissions: [PERMISSIONS.PRODUCT_EDIT] }
      },
      {
        path: "customer/coupon-manager",
        component: CouponManagerView,
        meta: { requiredPermissions: [PERMISSIONS.PROMOTION_CREATE] }
      },
      {
        path: "/product/category-manager/seo-config/:id",
        name: "CategorySeoConfig",
        component: CategorySeoConfigView,
        props: true,
      },
      {
        path: "product/collection/collection-product-list",
        name: "CollectionProductList",
        component: CollectionProductList,
      },
      {
        path: "product/collection/collection-product-form",
        name: "CollectionProductForm",
        component: CollectionProductForm,
        props: true
      },
      {
        path: "product/collection/collection-product-form/:id",
        name: "CollectionProductFormEdit",
        component: CollectionProductForm,
        props: true
      },
      {
        path: "/product/label/label-initial",
        name: "LabelInitial",
        component: LabelInitial,
      },
      {
        path: "measurement-tables",
        name: "measurement-tables-list",
        component: MeasurementTablesListView,
      },
      {
        path: "measurement-tables/new",
        name: "measurement-table-new",
        component: MeasurementTableEditor,
      },
      {
        path: "measurement-tables/:id",
        name: "measurement-table-edit",
        component: MeasurementTableEditor,
        props: true,
      },
      {
        path: "measurement-tables/inline/new",
        name: "measurement-table-inline-new",
        component: MeasurementTableFormInline,
      },
      {
        path: "measurement-tables/inline/:id",
        name: "measurement-table-inline-edit",
        component: MeasurementTableFormInline,
        props: true,
      },
      {
        path: "coupons",
        name: "coupon-list",
        component: CouponManagerView,
      },
      {
        path: "coupons/new",
        name: "coupon-new",
        component: CouponForm,
        props: true,
      },
      {
        path: "coupons/:id",
        name: "coupon-edit",
        component: CouponForm,
        props: true,
      },
      {
        path: "settings",
        name: "store-physical-data",
        component: StorePhysicalData,
        meta: { requiredPermissions: [PERMISSIONS.STORE_SETTINGS] }
      },
      {
        path: "settings/minimum-order",
        component: MinimumOrderView,
        meta: { requiredPermissions: [PERMISSIONS.STORE_SETTINGS] }
      },
      {
        path: "settings/shipping",
        component: ShippingConfigView,
        meta: { requiredPermissions: [PERMISSIONS.SHIPPING_SETTINGS] }
      },
      {
        path: "settings/store-seo",
        component: StoreSeoSettingsView,
      },
      {
        path: "settings/social-media",
        component: SocialMediaSettings,
      },
      {
        path: "settings/brand-assets",
        name: "brand-assets",
        component: StoreBrandAssets,
      },
      {
        path: "settings/domain-manager",
        component: DomainUrlList,
      },
      {
        path: "settings/domain-manager/form",
        name: "domain-manager-form",
        component: DomainUrlForm,
        props: true,
      },
      {
        path: "settings/domain-manager/:id",
        name: "domain-manager-edit",
        component: DomainUrlForm,
        props: true,
      },
      {
        path: "settings/domain-manager/validate/:id",
        name: "domain-manager-validate",
        component: DomainUrlValidate,
        props: true,
      },
      {
        path: "/orders",
        component: OrderListView,
        meta: { requiredPermissions: [PERMISSIONS.ORDER_VIEW] }
      },
      {
        path: "orders/new",
        name: "order-new",
        component: OrderForm,
        props: true,
        meta: { requiredPermissions: [PERMISSIONS.ORDER_EDIT] }
      },
      {
        path: "orders/:id",
        name: "order-edit",
        component: OrderForm,
        props: true,
        meta: { requiredPermissions: [PERMISSIONS.ORDER_EDIT] }
      },
      {
        path: "orders/:id/view",
        name: "order-view",
        component: OrderForm,
        props: true,
        meta: { requiredPermissions: [PERMISSIONS.ORDER_VIEW] }
      },
      {
        path: "settings/url-redirect",
        component: UrlRedirectList,
      },
      {
        path: "settings/url-redirect/new",
        component: UrlRedirectForm,
      },
      {
        path: "settings/url-redirect/:id",
        component: UrlRedirectForm,
        props: true,
      },
      {
        path: "/user/change-password",
        name: "UserChangePassword",
        component: UserChangePassword
      },
      {
        path: "filemanager",
        component: FileManager,
      },
      {
        path: "settings/origin-cep",
        component: OriginCepForm
      },
      {
        path: "settings/email-notifications",
        name: "email-notifications",
        component: EmailNotificationSettings,
      },
      {
        path: "pages",
        name: "pages-list",
        component: PagesListView,
      },
      {
        path: "pages/new",
        name: "page-new",
        component: PageEditor,
        props: true,
      },
      {
        path: "pages/:id",
        name: "page-edit",
        component: PageEditor,
        props: true,
      },
      {
        path: "promotions",
        component: PromotionsListView,
        meta: { requiredPermissions: [PERMISSIONS.PROMOTION_VIEW] }
      },
      {
        path: "promotions/new",
        name: "promotion-new",
        component: () => import('@/views/promotion/PromotionEditor.vue'),
        props: true,
        meta: { requiredPermissions: [PERMISSIONS.PROMOTION_CREATE] }
      },
      {
        path: "promotions/:id",
        name: "promotion-edit",
        component: () => import('@/views/promotion/PromotionEditor.vue'),
        props: true,
        meta: { requiredPermissions: [PERMISSIONS.PROMOTION_EDIT] }
      },
      {
        path: "products/questions-answers",
        name: "questions-answers",
        component: QuestionsAnswers,
      },
      {
        path: "products/questions-answers/:id/reply",
        name: "question-reply",
        component: QuestionResponse,
        props: true,
      },
      {
        path: "products/predefined-answers",
        name: "predefined-answers",
        component: PredefinedAnswersManager,
      },
      {
        path: "products/predefined-answers/create",
        name: "predefined-answer-create",
        component: () => import('@/views/product/PredefinedAnswerEditor.vue'),
      },
      {
        path: "products/predefined-answers/:id/edit",
        name: "predefined-answer-edit",
        component: () => import('@/views/product/PredefinedAnswerEditor.vue'),
        props: true,
      },
      {
        path: "settings/store-mode",
        name: "store-mode",
        component: StoreModeView,
      },
      {
        path: "settings/payment-methods",
        name: "payment-methods",
        component: PaymentMethodsView,
      },
      {
        path: "marketing/community",
        name: "CommunityManagement",
        component: CommunityManagement,
      },
      {
        path: "blog",
        name: "blog-dashboard",
        component: BlogDashboard,
      },
      {
        path: "blog/categories",
        name: "blog-categories-list",
        component: BlogCategoryList,
      },
      {
        path: "blog/categories/new",
        name: "blog-category-new",
        component: BlogCategoryForm,
        props: true,
      },
      {
        path: "blog/categories/:id",
        name: "blog-category-edit",
        component: BlogCategoryForm,
        props: true,
      },
      {
        path: "blog/posts",
        name: "blog-posts-list",
        component: BlogPostList,
      },
      {
        path: "blog/posts/new",
        name: "blog-post-new",
        component: BlogPostForm,
        props: true,
      },
      {
        path: "blog/posts/:id",
        name: "blog-post-edit",
        component: BlogPostForm,
        props: true,
      },
      {
        path: "products/gift-card",
        name: "gift-card",
        component: GiftCardView
      },
      {
        path: "products/gift-card/new",
        name:"new-gift-card",
        component: NewGiftCardView
      },
      {
        path: "products/gift-card/:id",
        name:"edit-gift-card",
        component: NewGiftCardView,
        props: true
      },
      {
        path: "themes",
        name: "themes-gallery",
        component: ThemesGallery,
      }
    ],
  },
  // 404 Not Found routes
  {
    path: "/404",
    name: "NotFound",
    component: NotFound,
  },
  // Catch-all route for 404 - must be last
  {
    path: "/:pathMatch(.*)*",
    name: "CatchAll",
    component: NotFound,
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    }
    return { top: 0 };
  }
});

router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();
  const storeStore = useStoreStore();

  // Public routes that don't require authentication
  const publicRoutes = ["/login", "/forgot-password", "/password-recovery", "/request-signup", "/signup"];
  
  // User routes that require authentication but not store selection
  const userRoutes = ["/user/settings"];

  // Check if user has valid authentication token (not just flag)
  const hasValidUserToken = authStore.userToken && tokenManager.validateToken(authStore.userToken).isValid;
  
  // For authenticated users, also verify session is still valid on server
  let sessionValid = true;
  if (hasValidUserToken && !publicRoutes.includes(to.path)) {
    try {
      sessionValid = await authStore.checkSessionValidity();
    } catch (error) {
      sessionValid = false;
    }
  }
  
  const authenticated = authStore.userLoggedIn && hasValidUserToken && sessionValid;

  // If userLoggedIn flag is true but token is invalid or session invalid, force logout
  if (authStore.userLoggedIn && (!hasValidUserToken || !sessionValid)) {
    authStore.logout();
    next({ path: "/login" });
    return;
  }

  // Load selected store from storage if authenticated
  if (authenticated) {
    storeStore.loadSelectedStoreFromStorage();
  }

  // For store routes, also validate store token
  const isStoreRoute = !publicRoutes.includes(to.path) && !userRoutes.includes(to.path) && !to.path.startsWith("/user/") && to.path !== "/stores";
  
  if (authenticated && isStoreRoute && storeStore.hasSelectedStore()) {
    const hasValidStoreToken = authStore.storeToken && tokenManager.validateToken(authStore.storeToken).isValid;
    
    if (!hasValidStoreToken) {
      authStore.clearStoreToken();
      next({ path: "/stores" });
      return;
    }
  }

  if (!authenticated && !publicRoutes.includes(to.path)) {
    next({ path: "/login" });
  } else if (to.path === "/login" && authenticated) {
    // After login, redirect to store selection instead of dashboard
    next({ path: "/stores" });
  } else if (to.path === "/stores" && !authenticated) {
    // Store selection requires authentication
    next({ path: "/login" });
  } else if (authenticated && isStoreRoute && !storeStore.hasSelectedStore()) {
    // If authenticated but no store selected and trying to access store routes, redirect to store selection
    next({ path: "/stores" });
  } else {
    // Check permissions for protected routes
    const requiredPermissions = to.meta?.requiredPermissions;
    if (requiredPermissions && requiredPermissions.length > 0 && authenticated && isStoreRoute) {
      try {
        const { hasAllPermissions, permissionsLoaded, loading, loadUserPermissions } = useGlobalPermissions();
        
        // Se permissões não foram carregadas ainda, tentar carregar
        if (!permissionsLoaded.value && !loading.value) {
          await loadUserPermissions();
        }
        
        // Aguardar permissões carregarem com timeout
        const maxWaitTime = 10000; // 10 segundos
        const startTime = Date.now();
        
        while (!permissionsLoaded.value && (Date.now() - startTime) < maxWaitTime) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Se permissões ainda não carregaram, bloquear acesso
        if (!permissionsLoaded.value) {
          console.error('Timeout ao aguardar permissões - redirecionando para login');
          next({ path: "/login" });
          return;
        }
        
        // Verificar se usuário tem as permissões necessárias
        const hasAccess = hasAllPermissions(requiredPermissions);
        
        if (!hasAccess) {
          // Filtrar permissões válidas para evitar erro com undefined
          const validPermissions = requiredPermissions.filter(p => p && p.code);
          console.warn('Acesso negado para rota:', to.path, 'Permissões necessárias:', validPermissions.map(p => p.code));
          next({
            path: "/no-permission",
            query: {
              from: to.path,
              requiredPermissions: validPermissions.map(p => p.code).join(',')
            }
          });
          return;
        }
        
        
      } catch (error) {
        console.error('Erro ao verificar permissões:', error);
        // Em caso de erro, redirecionar para login (fail-safe)
        next({ path: "/login" });
        return;
      }
    }
    
    next();
  }
});

export default router;
