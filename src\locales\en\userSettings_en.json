{"title": "User Settings", "subtitle": "Manage your preferences and account settings", "sections": {"profile": {"title": "Profile", "myProfile": "My Profile", "subtitle": "Manage your personal information and account preferences", "profilePhoto": "Profile Photo", "profilePhotoDescription": "Add a photo to personalize your profile", "changePhoto": "Change Photo", "removePhoto": "Remove", "personalInformation": "Personal Information", "fullName": "Full Name", "fullNamePlaceholder": "Enter your full name", "email": "Email", "emailPlaceholder": "<EMAIL>", "phone": "Phone", "phonePlaceholder": "(11) 99999-9999", "cpf": "CPF", "cpfPlaceholder": "000.000.000-00", "cpfHint": "CPF cannot be changed", "preferences": "Preferences", "language": "Language", "languageDescription": "Choose interface language", "timezone": "Timezone", "timezoneDescription": "Set your local timezone", "saving": "Saving...", "saveChanges": "Save Changes", "profileUpdated": "Profile updated successfully!", "profileUpdateError": "Error saving profile", "profileLoadError": "Error loading profile"}, "security": {"title": "Security", "settings": "Security Settings", "subtitle": "Manage your account security and monitor activities", "changePassword": "Change Password", "twoFactorAuth": "Two-Factor Authentication", "sessionsAndLogs": "Sessions and Logs", "authenticatorApp": "Authenticator App", "authenticatorDescription": "Use an authenticator app to add an extra layer of security", "enabled": "Enabled", "disabled": "Disabled", "activate": "Activate", "configure": "Configure", "activeSessions": "Active Sessions", "sessionManagement": "Session Management", "sessionDescription": "Monitor and manage your active sessions", "viewAllSessions": "View All Sessions", "activityMonitoring": "Activity Monitoring", "recentActivity": "Recent Activity", "activityDescription": "Track recent activities on your account", "viewActivityLog": "View Activity Log", "twoFactorEnabled": "Two-factor authentication enabled", "twoFactorDisabled": "Two-factor authentication disabled", "twoFactorError": "Error changing two-factor authentication"}, "twoFactor": {"title": "Two-Factor Authentication", "description": "Use an authenticator app to add an extra layer of security", "statusEnabled": "Enabled", "statusDisabled": "Disabled", "enable": "Enable", "disable": "Disable", "configure": "Configure", "setupTitle": "Set Up TOTP Authentication", "setupDescription": "Configure your authenticator app to get verification codes", "qrInstructions": "Scan the QR code below with your authenticator app:", "manualInstructions": "Or manually enter this key in your app:", "appSuggestions": "Recommended apps: Google Authenticator, Authy, Microsoft Authenticator", "recommended": "Recommended", "enterCode": "Enter the code from your app", "codePlaceholder": "000000", "verifySetup": "Verify and Activate", "verifying": "Verifying...", "disableTitle": "Disable TOTP", "disableDescription": "Enter your current password to disable app authentication", "currentPassword": "Current password", "currentPasswordPlaceholder": "Enter your current password", "disabling": "Disabling...", "totpEnabled": "TOTP enabled successfully!", "totpDisabled": "TOTP disabled successfully!", "invalidCode": "Invalid code. Please try again.", "invalidPassword": "Incorrect password", "setupError": "Error setting up TOTP", "disableError": "Error disabling TOTP", "generatingQR": "Generating QR code..."}, "billing": {"title": "Billing", "paymentMethods": "Payment Methods", "subscriptions": "Subscriptions"}, "stores": {"title": "Stores", "myStores": "My Stores", "invites": "<PERSON><PERSON><PERSON>"}, "notifications": {"title": "Notifications", "email": "Email Notifications"}, "account": {"title": "Account", "myProfile": "My Profile", "preferences": "Preferences", "settings": "Account <PERSON><PERSON>", "contacts": "Contacts"}}, "changePassword": "Change Password", "changePasswordDescription": "Change Access Password", "changePasswordInstructions": "For your security, choose a strong and unique password that you don't use elsewhere.", "currentPassword": "Current Password", "currentPasswordPlaceholder": "Enter your current password", "newPassword": "New Password", "newPasswordPlaceholder": "Enter your new password", "confirmNewPassword": "Confirm New Password", "confirmNewPasswordPlaceholder": "Enter your new password again", "securityNoticeTitle": "Account Security", "securityNoticeText": "Your password will be encrypted and stored securely. We recommend using a unique password that contains uppercase letters, lowercase letters, numbers, and symbols.", "passwordRequirements": "Password Requirements", "requirementUpperCase": "At least one uppercase letter (A-Z)", "requirementLowerCase": "At least one lowercase letter (a-z)", "requirementNumber": "At least one number (0-9)", "requirementSpecialChar": "At least one special character (!@#$%)", "requirementMinLength": "Minimum 8 characters", "strengthWeak": "Weak", "strengthMedium": "Medium", "strengthGood": "Good", "strengthStrong": "Strong", "cancel": "Cancel", "updatePassword": "Update Password", "updating": "Updating...", "save": "Save", "confirmPassword": "Current password is required", "newPasswordRequired": "New password is required", "confirmNewPasswordRequired": "Password confirmation is required", "invalidCurrentPassword": "Current password is incorrect", "password": {"mfaTitle": "Confirm password change", "mfaDescription": "For your security, confirm your identity before changing your password"}, "mfa": {"title": "Security Verification", "description": "For your security, confirm your identity", "emailSent": "Code sent to", "checkInbox": "Check your inbox and spam folder", "totpInstruction": "Enter the code from your authenticator app", "emailCode": "Email code", "totpCode": "TOTP code", "verify": "Verify", "verifying": "Verifying...", "resend": "Resend", "resendIn": "Resend in {seconds}s", "invalidCode": "Invalid code. Please try again.", "codeExpired": "Code expired. A new code has been sent.", "verificationSuccess": "Verification completed successfully!", "verificationError": "Verification error. Please try again."}, "sessions": {"invalidatedTitle": "Other sessions disconnected", "invalidatedMessage": "All other sessions have been disconnected for security after the password change."}, "common": {"cancel": "Cancel", "confirm": "Confirm", "close": "Close"}, "billing": {"savedCards": "Saved Cards", "addCard": "Add Card", "addFirstCard": "Add First Card", "addNewCard": "Add New Card", "addNewCardOption": "Add new credit card", "noCardsTitle": "No saved cards", "noCardsDescription": "Add a card to make future payments easier", "noSavedCards": "You have no saved cards", "defaultCard": "De<PERSON>ult Card", "setDefault": "Set as <PERSON><PERSON><PERSON>", "updateCard": "Update Card", "editCard": "Edit Card", "cardNumber": "Card Number", "cardNumberPlaceholder": "0000 0000 0000 0000", "cardHolder": "Cardholder Name", "cardHolderPlaceholder": "Name as it appears on card", "expiryDate": "Expiry Date", "cvv": "CVV", "setAsDefault": "Set as default card", "saveCard": "Save card for future use", "securityMessage": "Your payment information is protected with bank-level encryption", "paymentHistory": "Payment History", "downloadInvoices": "Download Invoices", "date": "Date", "description": "Description", "amount": "Amount", "status": {"paid": "Paid", "pending": "Pending", "failed": "Failed"}, "viewInvoice": "View Invoice", "download": "Download", "deleteCardTitle": "Delete Card", "deleteCardMessage": "Are you sure you want to delete this card? This action cannot be undone.", "zipCode": "ZIP Code", "zipCodePlaceholder": "00000-000", "street": "Street", "streetPlaceholder": "Street name", "number": "Number", "numberPlaceholder": "123", "complement": "Complement", "complementPlaceholder": "Apt, block, etc.", "neighborhood": "Neighborhood", "neighborhoodPlaceholder": "Neighborhood name", "city": "City", "cityPlaceholder": "City name", "state": "State", "statePlaceholder": "Select state", "country": "Country", "countryPlaceholder": "Brazil", "saveAddress": "Save Address", "validation": {"invalidCardNumber": "Invalid card number", "cardHolderMinLength": "Name must be at least 3 characters", "invalidExpiryDate": "Invalid expiry date (MM/YY)", "invalidCvv": "CVV must be 3 or 4 digits", "invalidZipCode": "Invalid ZIP code"}, "success": {"cardAdded": "Card added successfully", "cardUpdated": "Card updated successfully", "cardDeleted": "Card deleted successfully", "defaultCardSet": "Default card set successfully"}, "errors": {"loadCards": "Error loading cards", "loadHistory": "Error loading history", "setDefault": "Error setting default card", "deleteCard": "Error deleting card"}}, "subscriptions": {"currentPlan": "Current Plan", "availablePlans": "Available Plans", "availableUpgrades": "Available Upgrades", "noActiveSubscription": "No active subscription", "noActiveDescription": "Choose a plan to start using all features", "choosePlan": "Choose <PERSON>", "recommended": "Recommended", "monthly": "Monthly", "yearly": "Yearly", "savePercent": "Save {percent}%", "subscribe": "Subscribe", "upgrade": "Upgrade", "downgrade": "Downgrade", "upgradeTo": "Upgrade to {plan}", "manage": "Manage", "cancel": "Cancel", "pause": "Pause", "status": "Status", "nextBilling": "Next Billing", "paymentMethod": "Payment Method", "amount": "Amount", "history": "History", "date": "Date", "action": "Action", "plan": "Plan", "periods": {"monthly": "month", "yearly": "year"}, "statuses": {"active": "Active", "cancelled": "Cancelled", "pending": "Pending", "paused": "Paused"}, "manageSubscription": "Manage Subscription", "changePaymentMethod": "Change Payment Method", "currentPaymentMethod": "Current Payment Method", "selectNewMethod": "Select New Method", "changeMethod": "Change Method", "billingAddress": "Billing Address", "noBillingAddress": "No billing address registered", "notifications": "Notifications", "billingReminders": "Billing Reminders", "billingRemindersDesc": "Receive reminders before billing date", "renewalNotices": "Renewal Notices", "renewalNoticesDesc": "Be notified about automatic renewals", "planUpdates": "Plan Updates", "planUpdatesDesc": "Receive information about new features and plans", "dangerZone": "Danger Zone", "pauseSubscription": "Pause Subscription", "pauseSubscriptionDesc": "Temporarily pause your subscription", "cancelSubscription": "Cancel Subscription", "cancelSubscriptionDesc": "Permanently cancel your subscription", "cancelTitle": "Cancel Subscription", "cancelMessage": "Are you sure you want to cancel your subscription? You will lose access to all premium features.", "upgradePlan": "Plan Upgrade", "currentPrice": "Current Price", "newPrice": "New Price", "difference": "Difference", "upgradeInfo": "Billing will be prorated for the remaining time of the current period", "confirmUpgradeTitle": "Confirm Upgrade", "confirmUpgradeMessage": "You are upgrading from {currentPlan} to {newPlan}. The amount will change from {currentPrice} to {newPrice} (difference of {difference}).", "success": {"subscribed": "Subscription successful", "upgraded": "Upgrade successful", "planChanged": "Plan changed successfully", "cancelled": "Subscription cancelled successfully", "paused": "Subscription paused successfully", "updated": "Subscription updated successfully", "paymentMethodChanged": "Payment method changed successfully", "addressSaved": "Address saved successfully", "notificationsUpdated": "Notification settings updated"}, "errors": {"loadCurrent": "Error loading current subscription", "loadPlans": "Error loading available plans", "loadHistory": "Error loading history", "subscribe": "Error subscribing", "changePlan": "Error changing plan", "upgrade": "Error upgrading", "cancel": "Error cancelling subscription", "pause": "Error pausing subscription", "changePaymentMethod": "Error changing payment method", "updateNotifications": "Error updating notification settings"}}}