<template>
  <div class="theme-backup-history">
    <!-- Header -->
    <div class="backup-header">
      <div class="header-content">
        <div class="header-text">
          <h2 class="backup-title">
            <span v-if="props.theme">{{ $t('themes.backup.historyOf', { themeName: props.theme.name }) }}</span>
            <span v-else>{{ $t('themes.backup.title') }}</span>
          </h2>
          <p class="backup-subtitle">{{ $t('themes.backup.subtitle') }}</p>
        </div>
        
        <!-- Stats Cards -->
        <div class="backup-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <HugeiconsIcon :icon="DataRecoveryIcon" />
            </div>
            <div class="stat-content">
              <span class="stat-value">{{ computedBackupStats.totalBackups || 0 }}</span>
              <span class="stat-label">{{ $t('themes.backup.stats.totalBackups') }}</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <HugeiconsIcon :icon="HardDriveIcon" />
            </div>
            <div class="stat-content">
              <span class="stat-value">{{ formatFileSize(computedBackupStats.totalSizeBytes || 0) }}</span>
              <span class="stat-label">{{ $t('themes.backup.stats.totalSize') }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && backups.length === 0" class="backup-loading">
      <div class="loading-spinner"></div>
      <p>{{ $t('themes.backup.loading') }}</p>
    </div>

    <!-- Empty State -->
    <div v-else-if="!isLoading && (!backups || backups.length === 0)" class="backup-empty">
      <div class="empty-icon">
        <HugeiconsIcon :icon="DataRecoveryIcon" />
      </div>
      <h3>{{ $t('themes.backup.empty.title') }}</h3>
      <p v-if="props.theme">
        Nenhum backup encontrado para o tema "{{ props.theme.name }}".
        Os backups são criados automaticamente quando você troca de tema ou podem ser criados manualmente.
      </p>
      <p v-else>{{ $t('themes.backup.empty.description') }}</p>
    </div>

    <!-- Backup Groups -->
    <div v-else class="backup-groups">
      <!-- Warning Alert for Near Expiration -->
      <div v-if="backupsNearExpiration?.length > 0" class="expiration-alert">
        <HugeiconsIcon :icon="Alert02Icon" />
        <div class="alert-content">
          <span class="alert-title">{{ $t('themes.backup.alerts.nearExpiration') }}</span>
          <span class="alert-message">
            {{ $t('themes.backup.alerts.nearExpirationMessage', { count: backupsNearExpiration.length }) }}
          </span>
        </div>
      </div>

      <!-- Backup Tables by Theme -->
      <div
        v-for="group in filteredBackupGroups"
        :key="group.themeId"
        class="backup-group"
      >
        <!-- Group header - only show when displaying multiple themes -->
        <div v-if="!props.theme" class="group-header">
          <h3 class="group-title">{{ group.themeName || $t('themes.backup.table.themeNameFallback') }}</h3>
          <span class="group-count">{{ $t('themes.backup.table.backupCount', { count: (group.backups || []).length }) }}</span>
        </div>
        
        <div v-if="group.backups && group.backups.length > 0" class="backup-table-container">
          <table class="backup-table">
            <thead>
              <tr>
                <th class="col-type">{{ $t('themes.backup.table.headers.type') }}</th>
                <th class="col-date">{{ $t('themes.backup.table.headers.date') }}</th>
                <th class="col-size">{{ $t('themes.backup.table.headers.size') }}</th>
                <th class="col-files">{{ $t('themes.backup.table.headers.files') }}</th>
                <th class="col-status">{{ $t('themes.backup.table.headers.status') }}</th>
                <th class="col-restored">{{ $t('themes.backup.table.headers.restored') }}</th>
                <th class="col-actions">{{ $t('themes.backup.table.headers.actions') }}</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="backup in safeBackups(group.backups || [])"
                :key="backup.id || backup.backupId || Math.random()"
                class="backup-row"
                :class="{
                  'backup-row--expired': backup?.isExpired,
                  'backup-row--near-expiration': backup?.isNearExpiration
                }"
              >
                <!-- Type Column -->
                <td class="col-type">
                  <div class="backup-type">
                    <div class="type-icon" :class="`type-icon--${backup.typeColor || 'gray'}`">
                      <HugeiconsIcon v-if="backup.typeIcon" :icon="backup.typeIcon" />
                      <span v-else class="type-icon-fallback">📦</span>
                    </div>
                    <span class="type-label">{{ backup.typeLabel || 'Backup' }}</span>
                  </div>
                </td>
                
                <!-- Date Column -->
                <td class="col-date">
                  <span class="backup-date">{{ backup.formattedCreatedAt || '—' }}</span>
                </td>
                
                <!-- Size Column -->
                <td class="col-size">
                  <span class="backup-size">{{ backup.formattedSize || '—' }}</span>
                </td>
                
                <!-- Files Column -->
                <td class="col-files">
                  <span class="backup-files">{{ backup.fileCount || 0 }}</span>
                </td>
                
                <!-- Status Column -->
                <td class="col-status">
                  <div v-if="backup.isExpired" class="status-badge status-badge--expired">
                    <HugeiconsIcon :icon="CancelIcon" />
                    Expirado
                  </div>
                  <div v-else-if="backup.isNearExpiration" class="status-badge status-badge--warning">
                    <HugeiconsIcon :icon="AlertCircleIcon" />
                    {{ backup.daysUntilExpiration }} dia(s)
                  </div>
                  <div v-else class="status-badge status-badge--valid">
                    <HugeiconsIcon :icon="CheckmarkCircle01Icon" />
                    Válido
                  </div>
                </td>
                
                <!-- Restored Column -->
                <td class="col-restored">
                  <div v-if="backup.restorationCount && backup.restorationCount > 0" class="restoration-info">
                    <span class="restoration-count">
                      <HugeiconsIcon :icon="RotateClockwiseIcon" />
                      {{ backup.restorationCount }}x
                    </span>
                    <span v-if="backup.lastRestoredAt" class="last-restored">
                      {{ backup.formattedLastRestoredAt }}
                    </span>
                  </div>
                  <span v-else class="no-restoration">—</span>
                </td>
                
                <!-- Actions Column -->
                <td class="col-actions">
                  <div class="backup-actions">
                    <IluriaButton
                      v-if="!backup.isExpired"
                      variant="outline"
                      size="small"
                      :huge-icon="RotateClockwiseIcon"
                      :loading="isRestoringBackup"
                      @click="handleRestoreBackup(backup)"
                    >
                      {{ $t('themes.backup.actions.restore') }}
                    </IluriaButton>
                    
                    <IluriaButton
                      variant="ghost"
                      size="small"
                      color="danger"
                      :huge-icon="Delete01Icon"
                      @click="handleDeleteBackup(backup)"
                    >
                      {{ $t('themes.backup.actions.delete') }}
                    </IluriaButton>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Empty State for Group -->
        <div v-else class="backup-group-empty">
          <p>{{ $t('themes.backup.table.noBackupsForTheme') }}</p>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <IluriaModal
      v-model="showDeleteModal"
      :title="$t('themes.backup.delete.title')"
      max-width="400px"
    >
      <div class="delete-modal-content">
        <div class="delete-icon">
          <HugeiconsIcon :icon="Alert02Icon" />
        </div>
        <p class="delete-message">
          {{ $t('themes.backup.delete.message') }}
          <strong>{{ selectedBackup?.themeName }}</strong>?
        </p>
        <p class="delete-warning">{{ $t('themes.backup.delete.warning') }}</p>
      </div>
      
      <template #footer>
        <div class="modal-actions">
          <IluriaButton
            variant="ghost"
            @click="showDeleteModal = false"
          >
            {{ $t('common.cancel') }}
          </IluriaButton>
          
          <IluriaButton
            variant="solid"
            color="danger"
            @click="confirmDeleteBackup"
          >
            {{ $t('themes.backup.actions.delete') }}
          </IluriaButton>
        </div>
      </template>
    </IluriaModal>

    <!-- Restore Confirmation Modal -->
    <IluriaModal
      v-model="showRestoreModal"
      :title="$t('themes.backup.restore.title')"
      max-width="450px"
    >
      <div class="restore-modal-content">
        <div class="restore-icon">
          <HugeiconsIcon :icon="RotateClockwiseIcon" />
        </div>
        <p class="restore-message">
          {{ $t('themes.backup.restore.message') }}
          <strong>{{ selectedBackup?.themeName }}</strong>?
        </p>
        <div class="restore-info">
          <div class="info-item">
            <span class="info-label">{{ $t('themes.backup.modal.backupDate') }}</span>
            <span class="info-value">{{ selectedBackup?.formattedCreatedAt }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ $t('themes.backup.modal.files') }}</span>
            <span class="info-value">{{ $t('themes.backup.modal.fileCount', { count: selectedBackup?.fileCount || 0 }) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ $t('themes.backup.modal.size') }}</span>
            <span class="info-value">{{ selectedBackup?.formattedSize }}</span>
          </div>
        </div>
        <p class="restore-warning">{{ $t('themes.backup.restore.warning') }}</p>
      </div>
      
      <template #footer>
        <div class="modal-actions">
          <IluriaButton
            variant="ghost"
            @click="showRestoreModal = false"
          >
            {{ $t('common.cancel') }}
          </IluriaButton>
          
          <IluriaButton
            variant="solid"
            :loading="isRestoringBackup"
            @click="confirmRestoreBackup"
          >
            {{ $t('themes.backup.actions.restore') }}
          </IluriaButton>
        </div>
      </template>
    </IluriaModal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useThemeBackup } from '@/composables/useThemeBackup'

import {
  DataRecoveryIcon,
  HardDriveIcon,
  Alert02Icon,
  CheckmarkCircle01Icon,
  AlertCircleIcon,
  CancelIcon,
  RotateClockwiseIcon,
  Delete01Icon,
  SquareArrowDataTransferHorizontalIcon,
  UserIcon,
  Appointment02Icon
} from '@hugeicons-pro/core-stroke-standard'

import { HugeiconsIcon } from '@hugeicons/vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'

// Props
const props = defineProps({
  theme: {
    type: Object,
    default: null
  }
})

// Emits
const emits = defineEmits(['theme-restored'])

// Composables
const {
  backups,
  backupStats,
  isLoading,
  isRestoringBackup,
  backupsNearExpiration,
  loadBackups,
  loadBackupsByTheme,
  restoreBackup,
  deleteBackup,
  loadBackupStats,
  formatBackupForDisplay,
  formatFileSize
} = useThemeBackup()

// State
const showDeleteModal = ref(false)
const showRestoreModal = ref(false)
const selectedBackup = ref(null)

// Icon mapping
const iconMap = {
  'SquareArrowDataTransferHorizontalIcon': SquareArrowDataTransferHorizontalIcon,
  'UserIcon': UserIcon,
  'Appointment02Icon': Appointment02Icon,
  'DataRecoveryIcon': DataRecoveryIcon
}

// Safe backup processing
const safeBackups = (backups) => {
  if (!Array.isArray(backups)) return []

  return backups
    .map(backup => {
      if (!backup) return null

      try {
        const formatted = formatBackupForDisplay(backup)

        // Convert icon string to actual icon component
        let typeIcon = null
        if (formatted.typeIcon && typeof formatted.typeIcon === 'string') {
          typeIcon = iconMap[formatted.typeIcon] || DataRecoveryIcon
        } else if (formatted.typeIcon) {
          typeIcon = formatted.typeIcon
        }

        return {
          ...formatted,
          // Ensure required fields exist
          id: formatted.id || backup.id || backup.backupId,
          typeIcon: typeIcon,
          typeLabel: formatted.typeLabel || 'Backup',
          typeColor: formatted.typeColor || 'gray',
          isExpired: formatted.isExpired || false,
          isNearExpiration: formatted.isNearExpiration || false
        }
      } catch (error) {
        console.warn('Error formatting backup:', error, backup)
        return null
      }
    })
    .filter(Boolean)
}

// Computed properties
const filteredBackupGroups = computed(() => {
  const allBackups = safeBackups(backups.value || [])

  if (props.theme?.id) {
    // Se um tema específico foi passado, filtra apenas seus backups
    const themeBackups = allBackups.filter(backup => backup.themeId === props.theme.id)
    if (themeBackups.length === 0) return []

    return [{
      themeId: props.theme.id,
      themeName: props.theme.name,
      backups: themeBackups
    }]
  } else {
    // Se não há tema específico, agrupa todos os backups por tema
    const grouped = {}

    allBackups.forEach(backup => {
      const themeId = backup.themeId
      if (!grouped[themeId]) {
        grouped[themeId] = {
          themeId,
          themeName: backup.themeName,
          backups: []
        }
      }
      grouped[themeId].backups.push(backup)
    })

    return Object.values(grouped)
  }
})

// Computed stats - calcula estatísticas baseadas nos backups atuais
const computedBackupStats = computed(() => {
  const allBackups = safeBackups(backups.value || [])

  if (props.theme?.id) {
    // Para tema específico, calcula estatísticas apenas dos backups desse tema
    const themeBackups = allBackups.filter(backup => backup.themeId === props.theme.id)

    const totalBackups = themeBackups.length
    const totalSizeBytes = themeBackups.reduce((sum, backup) => {
      return sum + (backup.backupSizeBytes || 0)
    }, 0)

   
    return {
      totalBackups,
      totalSizeBytes,
      averageBackupSize: totalBackups > 0 ? Math.round(totalSizeBytes / totalBackups) : 0
    }
  } else {
    // Para todos os temas, usa as estatísticas globais ou calcula se não disponível
    if (backupStats.value && Object.keys(backupStats.value).length > 0) {
      return backupStats.value
    }

    // Fallback: calcula estatísticas de todos os backups
    const totalBackups = allBackups.length
    const totalSizeBytes = allBackups.reduce((sum, backup) => {
      return sum + (backup.backupSizeBytes || 0)
    }, 0)

    return {
      totalBackups,
      totalSizeBytes,
      averageBackupSize: totalBackups > 0 ? Math.round(totalSizeBytes / totalBackups) : 0
    }
  }
})

// Methods
const handleRestoreBackup = (backup) => {
  selectedBackup.value = backup
  showRestoreModal.value = true
}

const handleDeleteBackup = (backup) => {
  selectedBackup.value = backup
  showDeleteModal.value = true
}

const confirmRestoreBackup = async () => {
  if (selectedBackup.value) {
    // Callback para recarregar dados após restauração bem-sucedida
    const onRestoreCompleted = async () => {
      // Recarregar lista de temas para refletir o tema restaurado
      if (emits) {
        emits('theme-restored')
      }
      
      // Fechar modal
      showRestoreModal.value = false
      selectedBackup.value = null
    }
    
    const success = await restoreBackup(selectedBackup.value, onRestoreCompleted)
    if (success) {
      // Não fechar modal aqui - será fechado pelo callback do WebSocket
    }
  }
}

const confirmDeleteBackup = async () => {
  if (selectedBackup.value) {
    const success = await deleteBackup(selectedBackup.value)
    if (success) {
      showDeleteModal.value = false
      selectedBackup.value = null
    }
  }
}

// Initialize
onMounted(async () => {


  try {
    if (props.theme?.id) {
      // Se um tema específico foi passado, carrega apenas seus backups
     

      const themeBackups = await loadBackupsByTheme(props.theme.id)
    

      // Substitui os backups globais pelos do tema específico
      backups.value = themeBackups || []

      // Para tema específico, não carrega estatísticas gerais
      // As estatísticas serão calculadas apenas dos backups do tema
    } else {
      // Se não há tema específico, carrega todos os backups
     

      await loadBackups()

      // Carrega estatísticas gerais apenas quando não há tema específico
      await loadBackupStats()
    }

  
  } catch (error) {
    console.error('❌ Error loading backups:', error)
  }
})
</script>

<style scoped>
.theme-backup-history {
  padding: 1.5rem;
}

.backup-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.header-content {
  flex: 1;
}

.backup-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin-bottom: 0.5rem;
}

.backup-subtitle {
  font-size: 1rem;
  color: var(--iluria-color-text-secondary);
  margin-bottom: 1.5rem;
}

.backup-stats {
  display: flex;
  gap: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--iluria-color-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--iluria-color-border);
}

.stat-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--iluria-color-primary-light);
  border-radius: 0.375rem;
  color: var(--iluria-color-primary);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
}

.backup-loading, .backup-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--iluria-color-background-muted);
  border-radius: 50%;
  color: var(--iluria-color-text-tertiary);
  margin-bottom: 1rem;
}

.expiration-alert {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(251, 113, 133, 0.1);
  border: 1px solid rgba(251, 113, 133, 0.2);
  border-radius: 0.5rem;
  color: #dc2626;
  margin-bottom: 1.5rem;
}

.alert-content {
  display: flex;
  flex-direction: column;
}

.alert-title {
  font-weight: 600;
  font-size: 0.875rem;
}

.alert-message {
  font-size: 0.8125rem;
  opacity: 0.8;
}

.backup-group {
  margin-bottom: 2rem;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.group-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.group-count {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  background: var(--iluria-color-background-muted);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.backup-table-container {
  background: var(--iluria-color-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--iluria-color-border);
  overflow-x: auto;
}

.backup-table {
  width: 100%;
  border-collapse: collapse;
}

.backup-table th {
  background: var(--iluria-color-background-muted);
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  border-bottom: 1px solid var(--iluria-color-border);
}

.backup-table th:first-child {
  border-top-left-radius: 0.5rem;
}

.backup-table th:last-child {
  border-top-right-radius: 0.5rem;
}

.backup-row {
  border-bottom: 1px solid var(--iluria-color-border);
  transition: background-color 0.2s;
}

.backup-row:hover {
  background: var(--iluria-color-background-muted);
}

.backup-row:last-child {
  border-bottom: none;
}

.backup-row--expired {
  opacity: 0.6;
  background: rgba(239, 68, 68, 0.02);
}

.backup-row--near-expiration {
  background: rgba(251, 113, 133, 0.02);
}

.backup-table td {
  padding: 1rem;
  vertical-align: middle;
}

.col-type { width: 20%; }
.col-date { width: 15%; }
.col-size { width: 10%; }
.col-files { width: 8%; }
.col-status { width: 12%; }
.col-restored { width: 15%; }
.col-actions { width: 20%; }

.backup-group-empty {
  padding: 2rem;
  text-align: center;
  background: var(--iluria-color-background-muted);
  border: 1px solid var(--iluria-color-border);
  border-radius: 0.5rem;
  color: var(--iluria-color-text-secondary);
}

.backup-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.type-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
}

.type-icon--blue { background: rgba(59, 130, 246, 0.1); color: #2563eb; }
.type-icon--green { background: rgba(34, 197, 94, 0.1); color: #16a34a; }
.type-icon--purple { background: rgba(147, 51, 234, 0.1); color: #7c3aed; }
.type-icon--gray { background: rgba(107, 114, 128, 0.1); color: #6b7280; }

.type-label {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.backup-date,
.backup-size,
.backup-files {
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
}

.no-restoration {
  color: var(--iluria-color-text-tertiary);
  font-size: 0.875rem;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge--expired {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.status-badge--warning {
  background: rgba(251, 113, 133, 0.1);
  color: #be185d;
}

.status-badge--valid {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
}

.restoration-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.125rem;
  font-size: 0.75rem;
  color: var(--iluria-color-text-tertiary);
}

.restoration-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.backup-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Modal Styles */
.delete-modal-content,
.restore-modal-content {
  text-align: center;
  padding: 1rem 0;
}

.delete-icon,
.restore-icon {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 50%;
  color: #dc2626;
  margin: 0 auto 1rem;
}

.restore-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

.delete-message,
.restore-message {
  font-size: 1rem;
  color: var(--iluria-color-text-primary);
  margin-bottom: 1rem;
}

.delete-warning,
.restore-warning {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
}

.restore-info {
  background: var(--iluria-color-background-muted);
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: between;
  margin-bottom: 0.5rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
}

.info-value {
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .backup-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .backup-stats {
    width: 100%;
  }
  
  .stat-card {
    flex: 1;
  }
  
  .backup-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .backup-table {
    min-width: 700px;
  }
  
  .backup-table th,
  .backup-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8125rem;
  }
  
  .col-type { width: 25%; }
  .col-date { width: 20%; }
  .col-size { width: 12%; }
  .col-files { width: 8%; }
  .col-status { width: 15%; }
  .col-restored { width: 20%; }
  
  .backup-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .backup-actions button {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
  
  .type-label {
    display: none;
  }
  
  .status-badge {
    font-size: 0.6875rem;
    padding: 0.125rem 0.375rem;
  }
  
  .restoration-info {
    font-size: 0.6875rem;
  }
}

@media (max-width: 480px) {
  .backup-table {
    min-width: 600px;
  }
  
  .backup-table th,
  .backup-table td {
    padding: 0.5rem 0.375rem;
  }
}
</style>