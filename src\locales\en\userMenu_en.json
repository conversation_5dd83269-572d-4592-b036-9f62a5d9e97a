{"currentUser": "Current user: {name}", "profile": "Profile", "myProfile": "My Profile", "preferences": "Preferences", "settings": "Settings", "accountSettings": "Account <PERSON><PERSON>", "security": "Security", "securitySettings": "Security Settings", "changePassword": "Change Password", "stores": "Stores", "allStores": "All stores", "logout": "Logout", "navbar": {"testEnvironment": {"tooltip": "Preview your store"}, "fallbacks": {"storeLogo": "Store Logo", "storeName": "Store", "userName": "User", "storeUrl": "store.iluria.com", "userEmail": "<EMAIL>"}, "navigation": {"sales": {"title": "Sales", "orders": {"name": "Orders", "description": "Check the list of orders placed in the store"}, "coupons": {"name": "Discount Coupons", "description": "Create and manage discount coupons"}, "minimumOrder": {"name": "Minimum Order", "description": "Configure the store to have a minimum order value"}, "giftCards": {"name": "Gift Cards", "description": "View registered gift cards and register new ones"}, "customers": {"name": "Customers", "description": "Track your store's customer list"}, "promotions": {"name": "Promotions", "description": "Manage store promotions and discounts"}, "reports": {"name": "Reports", "description": "Track your store's performance"}}, "products": {"title": "Products", "newProduct": {"name": "New Product", "description": "Create new physical or digital products"}, "collections": {"name": "Collections", "description": "Register new product collections"}, "combinedProduct": {"name": "Combined Product", "description": "Combine products to create a unique product"}, "questionsAnswers": {"name": "Questions & Answers", "description": "Manage product questions and answers"}, "categories": {"name": "Categories", "description": "Manage product categories"}, "productList": {"name": "Product List", "description": "List of products available in the store"}, "attributesFilters": {"name": "Attributes & Filters", "description": "Manage product attributes and filters"}, "labels": {"name": "Labels", "description": "Manage product labels"}, "measurementTables": {"name": "Measurement Tables", "description": "Create and manage measurement tables for products"}, "organizeProducts": {"name": "Organize Products", "description": "Define the display order of products in the store"}}, "marketing": {"title": "Marketing", "community": {"name": "Community", "description": "Manage your store's customer community"}, "blog": {"name": "Blog", "description": "Manage your store's blog posts"}, "newsletter": {"name": "Newsletter", "description": "Manage subscriptions and send newsletters to your customers"}, "seoMainPage": {"name": "Main Page SEO", "description": "SEO settings for the store's main page"}}, "settings": {"title": "Settings", "storeData": {"name": "Store Data", "description": "General store and owner information"}, "shipping": {"name": "Shipping", "description": "Post Office, Local Delivery, Discounts and more"}, "domains": {"name": "Domains", "description": "Store domain settings"}, "pages": {"name": "Pages", "description": "Create, edit or delete pages with custom content"}, "storeCep": {"name": "Store ZIP Code", "description": "Set the origin ZIP code for shipping calculation"}, "paymentMethods": {"name": "Payment Methods", "description": "Manage store payment methods"}, "socialMedia": {"name": "Social Media", "description": "Facebook, Instagram, X, TikTok settings"}, "emailNotifications": {"name": "Email Notifications", "description": "Configure the notifications you want to receive by email"}, "redirects": {"name": "Redirects", "description": "Manage store URL redirects"}, "vacationMaintenance": {"name": "Vacation & Maintenance", "description": "Enable or disable maintenance or vacation modes"}, "teamManagement": {"name": "Team Management", "description": "Manage team users and permissions"}}, "layout": {"title": "Layout", "layout": {"name": "Layout", "description": "Manage themes, customize colors, fonts, images and components"}, "fileManagement": {"name": "File Management", "description": "Manage files and folders used in the store layout"}, "logoFavicon": {"name": "Logo & Favicon", "description": "Configure the store's logo and favicon images"}}}}}